"use client";
import React, { useState, useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import SectionBadge from "../ui/SectionBadge";
import OfferCard from "./OfferCard";
import offersData from "../../app/data/offersData.json";


const OffersSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const carouselRef = useRef(null);

  // Use data from offersData.json
  const offers = offersData;

  const handlePrevSlide = () => {
    if (canShowPrev) {
      const newSlide = currentSlide - 1;
      setCurrentSlide(newSlide);
      scrollToSlide(newSlide);
    }
  };

  const handleNextSlide = () => {
    if (canShowNext) {
      const newSlide = currentSlide + 1;
      setCurrentSlide(newSlide);
      scrollToSlide(newSlide);
    }
  };

  const scrollToSlide = (slideIndex) => {
    if (carouselRef.current) {
      const container = carouselRef.current;
      const slideWidth = container.clientWidth / 3; // Show 3 cards at a time
      const gap = 32; // Tailwind gap-8
      container.scrollTo({
        left: slideIndex * (slideWidth + gap / 3),
        behavior: "smooth",
      });
    }
  };

  const getVisibleCards = () => {
    // On mobile: 1 card, on tablet: 2 cards, on desktop: 3 cards
    if (typeof window !== "undefined") {
      if (window.innerWidth < 768) return 1;
      if (window.innerWidth < 1024) return 2;
      return 3;
    }
    return 3;
  };

  const canShowNext = currentSlide < offers.length - getVisibleCards();
  const canShowPrev = currentSlide > 0;

  const handleLearnMore = (offerId) => {
    console.log(`Learn more clicked for offer ${offerId}`);
  };

  return (
    <section className="pb-5 pt-10">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        {/* Header Section */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-[#0C2C7A] dark:text-white mb-6 leading-tight">
            Let's Check <span className="text-[#24BDC7]">Exclusive Offers</span>
          </h2>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Buttons - Desktop */}
          <div
            className={`hidden lg:flex absolute -left-4 top-[40%] transform -translate-y-1/2 z-10 ${
              !canShowPrev ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            <button
              onClick={handlePrevSlide}
              disabled={!canShowPrev}
              className={`w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl flex items-center justify-center text-[#24BDC7] transition-all duration-300 ${
                canShowPrev
                  ? "hover:bg-[#24BDC7] hover:text-white"
                  : "cursor-not-allowed opacity-50"
              }`}
            >
              <ChevronLeft size={24} />
            </button>
          </div>

          <div
            className={`hidden lg:flex absolute -right-4 top-[40%] transform -translate-y-1/2 z-10 ${
              !canShowNext ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            <button
              onClick={handleNextSlide}
              disabled={!canShowNext}
              className={`w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl flex items-center justify-center text-[#24BDC7] transition-all duration-300 ${
                canShowNext
                  ? "hover:bg-[#24BDC7] hover:text-white"
                  : "cursor-not-allowed opacity-50"
              }`}
            >
              <ChevronRight size={24} />
            </button>
          </div>

          {/* Offers Carousel */}
          <div className="overflow-hidden">
            <div
              ref={carouselRef}
              className="flex gap-4 sm:gap-6 lg:gap-8 transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${
                  currentSlide * (100 / getVisibleCards())
                }%)`,
              }}
            >
              {offers.map((offer) => (
                <div
                  key={offer.id}
                  className="flex-shrink-0   w-[96%]  sm:w-[47%]  lg:w-[30%] xl:w-[31%]"
                >
                  <OfferCard
                    image={offer.image}
                    discount={offer.discount}
                    title={offer.title}
                    description={offer.description}
                    onLearnMore={() => handleLearnMore(offer.id)}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Mobile Navigation - Arrows + Dots */}
          <div className="flex lg:hidden justify-center items-center mt-6 space-x-4">
            {/* Left Arrow */}
            <button
              onClick={handlePrevSlide}
              disabled={!canShowPrev}
              className="w-10 h-10 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center text-gray-600 transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Dots Indicator */}
            <div className="flex gap-2">
              {Array.from({
                length: Math.max(0, offers.length - getVisibleCards() + 1),
              }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setCurrentSlide(index);
                    scrollToSlide(index);
                  }}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === currentSlide
                      ? "bg-[#24BDC7] w-6"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}
                />
              ))}
            </div>

            {/* Right Arrow */}
            <button
              onClick={handleNextSlide}
              disabled={!canShowNext}
              className="w-10 h-10 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center text-gray-600 transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Desktop Navigation Dots */}
          <div className="hidden lg:flex justify-center mt-8 gap-2">
            {Array.from({
              length: Math.max(0, offers.length - getVisibleCards() + 1),
            }).map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentSlide(index);
                  scrollToSlide(index);
                }}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? "bg-[#24BDC7] w-8"
                    : "bg-gray-300 dark:bg-gray-600"
                }`}
              />
            ))}
          </div>
         
        </div>
      </div>
    </section>
  );
};

export default OffersSection;
