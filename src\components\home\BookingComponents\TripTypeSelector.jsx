// components/home/<USER>/TripTypeSelector.jsx
import React from 'react';

const TripTypeSelector = ({ tripType, onChange }) => {
  const tripTypes = [
    { value: 'oneWay', label: 'One Way' },
    { value: 'roundTrip', label: 'Round Trip' },
    { value: 'multiCity', label: 'Multi City' }
  ];

  return (
    <div className="flex gap-1 mb-1">
      {tripTypes.map((type) => (
        <label key={type.value} className="flex items-center cursor-pointer">
          <input 
            type="radio" 
            name="trip" 
            value={type.value}
            checked={tripType === type.value}
            onChange={(e) => onChange(e.target.value)}
            className="w-1.5 h-1.5 lg:w-2 lg:h-2 text-[#24BDC7] mr-1" 
          />
          <span className="text-[#0C2C7A] text-xs font-bold lg:text-xs">{type.label}</span>
        </label>
      ))}
    </div>
  );
};

export default TripTypeSelector;
