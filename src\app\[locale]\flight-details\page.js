"use client"
import Header from "@/components/home/<USER>";
import { FlightDetailPage } from "@/components/bookingDetail";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchFlightDetail, setCurrentFareId } from "@/redux/slices/flightDetailSlice";
import { useSearchParams } from "next/navigation";

export default function FlightDetailsPage() {
  const dispatch = useDispatch();
  const { detail: flightDetail, loading, error } = useSelector((state) => state.flightDetail);
  const searchParams = useSearchParams();

  useEffect(() => {
    // Get fareId from query parameters
    const fareId = searchParams.get('fareId');
    console.log("Flight Details Page - fareId from URL:", fareId);
    
    if (fareId) {
      // Set the current fareId in Redux state
      dispatch(setCurrentFareId(fareId));
    }
    
    // Add error handling for the API call
    dispatch(fetchFlightDetail(fareId)).catch((err) => {
      console.error("Error dispatching fetchFlightDetail:", err);
    });
  }, [dispatch, searchParams]);

  useEffect(() => {
    if (flightDetail) {
      console.log("Flight detail data from Redux store:", flightDetail);
    }
    console.log("Current state - loading:", loading, "error:", error, "flightDetail:", flightDetail);
  }, [flightDetail, loading, error]);

  const breadcrumbItems = [
    { label: 'Flight Details', href: '/flight-details' }
  ];

  // Loading state
  if (loading) {
    return (
      <>
        <Header
          backgroundImage="/assets/img/breadcrumb/01.jpg"
          height="min-h-[50vh]"
          heroTitle="Flight Details"
          heroSubtitle="Loading flight information..."
          breadcrumbItems={breadcrumbItems}
          className="-mt-50"
        />
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#24BDC7] mx-auto"></div>
            <p className="mt-4 text-gray-600 text-lg">Loading flight details...</p>
          </div>
        </div>
      </>
    );
  }

  // Error state
  if (error) {
    return (
      <>
        <Header
          backgroundImage="/assets/img/breadcrumb/01.jpg"
          height="min-h-[50vh]"
          heroTitle="Flight Details"
          heroSubtitle="Error loading flight information"
          breadcrumbItems={breadcrumbItems}
          className="-mt-50"
        />
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <strong className="font-bold">Error!</strong>
              <span className="block sm:inline"> {error}</span>
            </div>
            <button
              onClick={() => dispatch(fetchFlightDetail())}
              className="bg-[#24BDC7] hover:bg-[#1ea5ae] text-white font-bold py-2 px-4 rounded transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </>
    );
  }

  // Fallback data in case API fails
  const fallbackData = !flightDetail ? {
    "id": "fare-0001",
    "currency": "USD",
    "segments": [
      {
        "flightNumber": "LH7399",
        "airlineCode": "LH",
        "airlineDisplayName": "Lufthansa",
        "airlineRating": 4.2,
        "reviews": 1150,
        "passengers": {
          "adults": 2,
          "children": 0
        },
        "images": [
          "/assets/img/flight/single-1.jpg",
          "/assets/img/flight/single-2.jpg",
          "/assets/img/flight/single-3.jpg"
        ],
        "duration": "5h 39m",
        "departure": {
          "city": "New York",
          "cityCode": "JFK",
          "countryCode": "US",
          "countryName": "United States",
          "scheduledDeparture": "2025-12-18T00:00:00.000Z"
        },
        "arrival": {
          "city": "Los Angeles",
          "cityCode": "LAX",
          "countryCode": "US",
          "countryName": "United States",
          "scheduledArrival": "2025-12-18T05:39:00.000Z"
        }
      }
    ],
    "serviceClass": "ECONOMY",
    "baggageAllowance": "Cabin: 7kg, Check-in: 23kg",
    "refundable": "Partially Refundable",
    "price": 450,
    "discountRate": 0.10,
    "tax": 50,
    "totalPrice": 500,
    "flightType": "oneWay",
    "status": "available"
  } : null;

  return (
    <>
      <Header
        backgroundImage="/assets/img/breadcrumb/01.jpg"
        height="min-h-[50vh]"
        heroTitle="Flight Details"
        heroSubtitle="Complete information about your selected flight"
        breadcrumbItems={breadcrumbItems}
        className="-mt-50"
      />

      <FlightDetailPage
        apiFlightData={flightDetail || fallbackData}
        className="bg-gray-50"
      />
    </>
  );
}
