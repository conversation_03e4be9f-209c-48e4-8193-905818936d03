"use client";
import Booking from '@/components/home/<USER>';
import Header from '@/components/home/<USER>';
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import regionFlightData from '../../data/regionFlightData.json';
import PopularFlightSection from '@/components/popularFlight/PopularFlightSection';

const PopularFlight = () => {
    const searchParams = useSearchParams();
    const destination = searchParams.get('destination');
    const [regionData, setRegionData] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (destination) {
            const data = regionFlightData[destination];
            setRegionData(data || null);
        }
        setLoading(false);
    }, [destination]);

    // Default data if no destination is specified
    const defaultData = {
        name: "Popular Flights",
        description: "Discover amazing flight deals to destinations around the world. Find the perfect flight for your next adventure.",
        flights: []
    };

    const currentData = regionData || defaultData;
    const breadcrumbItems = [
        { label: 'Popular Flight', href: '/popularFlight' }
    ];

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-lg">Loading...</div>
            </div>
        );
    }

    return (
        <>
            <Header
                backgroundImage="/assets/img/breadcrumb/01.jpg"
                height="min-h-[50vh]"
                heroTitle={currentData.name}
                heroSubtitle={currentData.description}
                breadcrumbItems={breadcrumbItems}
                className="-mt-12"
            />

            {/* Booking Component */}
            <div className="relative lg:left-24 z-10 -mt-16 lg:-mt-24 mb-1 w-full lg:w-[85%]">
                <Booking />
            </div>

            {/* Region Description Section */}
            <section className="bg-white p-2">
                <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
                    <div className="text-start mb-12">
                        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#0C2C7A] dark:text-white mb-6">
                            Flights to {currentData.name}
                        </h2>
                        <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                            {currentData.description}
                        </p>
                    </div>
                </div>

                {/* Flight Cards Section */}
                <PopularFlightSection
                    flights={currentData.flights}
                    regionName={currentData.name}
                />
            </section>
        </>
    );
};

export default PopularFlight;