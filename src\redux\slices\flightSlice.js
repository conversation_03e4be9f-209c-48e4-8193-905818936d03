import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { searchFlights as searchFlightsApi } from "@/api/flightApi";

// Async thunk
export const searchFlights = createAsyncThunk(
  "flights/searchFlights",
  async (payload, { rejectWithValue }) => {
    try {
      const data = await searchFlightsApi(payload);
      return data; // contains { flights: [...] }
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || "Failed to fetch flights");
    }
  }
);

const flightSlice = createSlice({
  name: "flights",
  initialState: {
    flights: [],
    loading: false,
    error: null,
  },
  reducers: {
    resetFlights: (state) => {
      state.flights = [];
      state.loading = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(searchFlights.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchFlights.fulfilled, (state, action) => {
        state.loading = false;
        console.log("Flights response:", action.payload);
        state.flights = action.payload.flights || [];
      })
      .addCase(searchFlights.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { resetFlights } = flightSlice.actions;
export default flightSlice.reducer;
