// src/redux/slices/flightBookingSlice.js
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { bookFlight } from "@/api/flightBookingApi";

// Async thunk for booking flight
export const createFlightBooking = createAsyncThunk(
  "flightBooking/createFlightBooking",
  async (bookingData, { rejectWithValue }) => {
    try {
      const res = await bookFlight(bookingData);
      return res.data;
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.message || "Failed to book flight"
      );
    }
  }
);

const flightBookingSlice = createSlice({
  name: "flightBooking",
  initialState: {
    booking: null, // booking response object
    loading: false,
    error: null,
  },
  reducers: {
    clearBooking: (state) => {
      state.booking = null;
      state.error = null;
      state.loading = false;
    },
    clearBookingError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createFlightBooking.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createFlightBooking.fulfilled, (state, action) => {
        state.loading = false;
        state.booking = action.payload;
      })
      .addCase(createFlightBooking.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const {
  clearBooking,
  clearBookingError
} = flightBookingSlice.actions;

export default flightBookingSlice.reducer;
