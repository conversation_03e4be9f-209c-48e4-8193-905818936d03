import React, { useState } from 'react';
import { Eye, EyeOff, User, Mail, Lock } from 'lucide-react';

const FormInput = ({ 
  type = "text",
  placeholder,
  value,
  onChange,
  required = false,
  className = "",
  icon,
  error,
  ...props 
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const getIcon = () => {
    switch (icon) {
      case 'user':
        return <User className="w-5 h-5 text-gray-400" />;
      case 'email':
        return <Mail className="w-5 h-5 text-gray-400" />;
      case 'password':
        return <Lock className="w-5 h-5 text-gray-400" />;
      default:
        return null;
    }
  };

  const inputType = type === 'password' && showPassword ? 'text' : type;

  return (
    <div className="relative">
      <div className={`relative flex items-center border-2 rounded-lg transition-all duration-200 ${
        error 
          ? 'border-red-500 bg-red-50' 
          : isFocused 
            ? 'border-[#24BDC7] bg-white' 
            : 'border-gray-200 bg-gray-50'
      } ${className}`}>
        
        {/* Left Icon */}
        {icon && (
          <div className="absolute left-3 flex items-center">
            {getIcon()}
          </div>
        )}

        {/* Input Field */}
        <input
          type={inputType}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          required={required}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={`w-full py-2 px-4 bg-transparent text-gray-800 placeholder-gray-400 outline-none rounded-lg ${
            icon ? 'pl-12' : 'pl-4'
          } ${type === 'password' ? 'pr-12' : 'pr-4'}`}
          {...props}
        />

        {/* Password Toggle */}
        {type === 'password' && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
          >
            {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
          </button>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};

export default FormInput;
