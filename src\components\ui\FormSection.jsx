"use client";
import React from 'react';

const FormSection = ({ 
  title, 
  subtitle, 
  children, 
  className = "",
  icon: Icon 
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          {Icon && <Icon className="text-[#0C2C7A]" size={24} />}
          <h2 className="text-xl font-bold text-[#0C2C7A]">{title}</h2>
        </div>
        {subtitle && (
          <p className="text-gray-600 text-sm">{subtitle}</p>
        )}
      </div>
      {children}
    </div>
  );
};

export default FormSection;
