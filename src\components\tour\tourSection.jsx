import tourData from '../../app/data/tourData.json';
import SectionBadge from '../ui/SectionBadge';
import ActionButton from '../ui/ActionButton';
import { useState, useEffect } from 'react';
import TourCard from './tourCard';

export default function TourSection() {
  const [activeFilter, setActiveFilter] = useState('All Tour');
  const [filteredTours, setFilteredTours] = useState(tourData.slice(0, 8));
  const [itemsPerView, setItemsPerView] = useState(4); // Default to 4 for desktop

  // Filter categories
  const filterCategories = [
    { id: 'all', label: 'All Tour', type: null },
    { id: 'historical', label: 'Historical', type: 'Historical' },
    { id: 'weekend', label: 'Weekend Trip', type: 'Weekend' },
    { id: 'special', label: 'Special Tour', type: 'Special' },
    { id: 'holiday', label: 'Holiday Tour', type: 'Holiday' }
  ];

  // Filter tours based on selected category
  useEffect(() => {
    if (activeFilter === 'All Tour') {
      setFilteredTours(tourData.slice(0, 8));
    } else {
      const categoryType = filterCategories.find(cat => cat.label === activeFilter)?.type;
      const filtered = tourData.filter(tour => tour.type === categoryType).slice(0, 8);
      setFilteredTours(filtered);
    }
  }, [activeFilter]);

  useEffect(() => {
    const updateItemsPerView = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth < 640) {
          setItemsPerView(1); // Small screens: 1 card
        } else if (window.innerWidth < 1024) {
          setItemsPerView(3); // Tablet: 3 cards
        } else {
          setItemsPerView(4); // Desktop: 4 cards
        }
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  return (
    <section className="sky-blue-bg bg-[url('/assets/img/shape/01.png')] bg-cover bg-center">
      <div className="container max-w-6xl mx-auto py-5 sm:py-10 md:py-12">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-8 sm:mb-12 md:mb-16">
          <div className="text-center lg:text-left mb-6 lg:mb-0">
            <SectionBadge text="TOURS" isMobile={false} />
            <h2 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold text-[#0C2C7A] my-4">
              Our Most Popular Tours
            </h2>
          </div>

          {/* Filter Buttons */}
          <div className="bg-sky-100 p-1 rounded-t-full rounded-bl-full flex flex-wrap justify-center lg:justify-end gap-2 sm:gap-3 mt-2">
            {filterCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.label)}
                className={`px-3 sm:px-4 py-2 rounded-t-full rounded-bl-full text-xs sm:text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                  activeFilter === category.label
                    ? 'bg-[#24BDC7] text-white shadow-md'
                    : 'bg-white text-gray-600 hover:bg-gray-200'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tour Cards Grid */}
        <div
          className="grid gap-2 sm:gap-4"
          style={{ gridTemplateColumns: `repeat(${itemsPerView}, minmax(0, 1fr))` }}
        >
          {filteredTours.map((tour, index) => (
            <TourCard key={`${tour.type}-${index}`} {...tour} />
          ))}
        </div>

        {/* Show message if no tours found */}
        {filteredTours.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.5a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-600 mb-2">No tours found</h3>
            <p className="text-gray-500">Try selecting a different category to see available tours.</p>
          </div>
        )}
      </div>
      </div>
    </section>
  );
}