"use client";
import React from 'react';
import SidebarWidgets from '../ui/SidebarWidgets';

const SidebarExample = () => {
  // Sample data for components
  const sampleData = {
    faqData: [
      {
        question: "What Are The Charges Of Services ?",
        answer: "We denounce with righteous indignation and dislike men who are so beguiled and demoralized by the charms of pleasure of the moment, so blinded by desire odio dignissim quam."
      },
      {
        question: "How Can I Become A Member ?",
        answer: "To become a member, simply sign up on our website and choose your preferred membership plan. You'll get instant access to all member benefits."
      },
      {
        question: "Can I Upgrade My Plan Any Time ?",
        answer: "Yes, you can upgrade your plan at any time from your account dashboard. The changes will take effect immediately."
      }
    ],

    contactData: {
      title: "Get A Question?",
      description: "It is a long established fact that a reader will be distracted by the readable content layout.",
      phone: "****** 4567 897",
      email: "<EMAIL>"
    },

    organizerData: {
      title: "Organized By",
      organizerName: "Roltak Travel Agency",
      memberSince: "Member Since 2025",
      avatar: "/assets/img/team/01.jpg"
    },

    bookingData: {
      price: "$450.00",
      originalPrice: "$500.00",
      journeyDate: "9/11/2025",
      journeyDay: "Thursday",
      returnDate: "9/12/2025",
      returnDay: "Friday",
      passengers: "2 Passenger",
      passengerClass: "Business",
      views: "250 Views",
      shares: "4 Share"
    },

    whyBookData: {
      title: "Why Book With Us?",
      features: [
        {
          icon: "Shield",
          title: "Best Price Guarantee",
          description: "We guarantee the best prices for all our services"
        },
        {
          icon: "Headphones", 
          title: "24/7 Customer Care",
          description: "Round the clock customer support for your convenience"
        },
        {
          icon: "MapPin",
          title: "Hand Picked Tours & Activities", 
          description: "Carefully selected tours and activities for the best experience"
        },
        {
          icon: "Plane",
          title: "Free Travel Insurance",
          description: "Complimentary travel insurance for your peace of mind"
        },
        {
          icon: "Car",
          title: "Comfortable And Hygienic Vehicle",
          description: "Clean and comfortable vehicles for your journey"
        }
      ]
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h1 className="text-3xl font-bold text-[#0C2C7A] mb-4">
              Flight Details Page
            </h1>
            <p className="text-gray-600 mb-6">
              This is the main content area. The sidebar widgets are displayed on the right side.
            </p>
            <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
              <span className="text-gray-500">Main Content Area</span>
            </div>
          </div>
        </div>

        {/* Sidebar with Widgets */}
        <div className="lg:col-span-1">
          <SidebarWidgets
            showFAQ={true}
            showContact={true}
            showOrganizer={true}
            showBooking={true}
            showWhyBook={true}
            faqData={sampleData.faqData}
            contactData={sampleData.contactData}
            organizerData={sampleData.organizerData}
            bookingData={sampleData.bookingData}
            whyBookData={sampleData.whyBookData}
          />
        </div>
      </div>
    </div>
  );
};

export default SidebarExample;
