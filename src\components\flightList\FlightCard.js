import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ActionButton from "../ui/ActionButton";
import {
  faPlaneDeparture,
  faPlaneArrival,
  faCircle,
} from "@fortawesome/free-solid-svg-icons";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { useRouter } from "@/i18n/navigation";

const FlightCard = ({ flight }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [activeTab, setActiveTab] = useState("baggage");
  const [activeFlightTab, setActiveFlightTab] = useState("outbound"); // For round trip flight switching
  const router = useRouter();

  const {
    id,
    fareId, // Add fareId from flight data
    flightName = "Unknown Airline",
    departureCity = "Unknown",
    departureCode = "UNK",
    arrivalCity = "Unknown",
    arrivalCode = "UNK",
    departureDate = "",
    arrivalDate = "",
    departureTime = "",
    arrivalTime = "",
    duration = "0h 0m",
    baggage = { cabin: "7 kg", checkIn: "20 kg" },
    stops = "Non Stop",
    price = 0,
    discount = 0,
    image = "",
    refund = "",
    isRoundTrip = false,
    returnFlight = {},
    isMultiCity = false,
    multiCityFlights = [],
  } = flight || {};

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  const handleSelectFlight = () => {
    // Use fareId if available, otherwise fallback to id
    const selectedFareId = fareId || id;
    console.log("Selected flight with fareId:", selectedFareId);
    
    if (selectedFareId) {
      // Navigate to flight details page with fareId as query parameter
      router.push(`/flight-details?fareId=${selectedFareId}`);
    } else {
      console.error("No fareId or id available for selected flight");
    }
  };

  const finalPrice =
    discount > 0 ? Math.round(price * (1 - discount / 100)) : price;

  // Parse stops to get number of stops
  const getStopCount = (stops) => {
    if (
      !stops ||
      stops.toLowerCase().includes("non stop") ||
      stops.toLowerCase().includes("nonstop") ||
      stops.toLowerCase().includes("direct")
    ) {
      return 0;
    }
    const match = stops.match(/(\d+)/);
    return match ? parseInt(match[1]) : 0;
  };

  const stopCount = getStopCount(stops);

  // Render stop indicators
  const renderStops = (size = "small") => {
    if (stopCount === 0) return null;

    const indicators = [];
    let circleSize, spacing;

    switch (size) {
      case "small":
        circleSize = "text-[8px]";
        spacing = "mx-0.5";
        break;
      case "medium":
        circleSize = "text-[9px]";
        spacing = "mx-1";
        break;
      case "large":
        circleSize = "text-[10px]";
        spacing = "mx-1.5";
        break;
      default:
        circleSize = "text-[8px]";
        spacing = "mx-0.5";
    }

    for (let i = 0; i < stopCount; i++) {
      indicators.push(
        <FontAwesomeIcon
          key={i}
          icon={faCircle}
          className={`${circleSize} text-[#0C2C82] ${spacing}`}
        />
      );
    }
    return indicators;
  };

  // Render stops for specific flight (used for return flights)
  const renderStopsForFlight = (flight, size = "small") => {
    if (!flight) return renderStops(size);
    
    const flightStopCount = getStopCount(flight.stops);
    if (flightStopCount === 0) return null;

    const indicators = [];
    let circleSize, spacing;

    switch (size) {
      case "small":
        circleSize = "text-[8px]";
        spacing = "mx-0.5";
        break;
      case "medium":
        circleSize = "text-[9px]";
        spacing = "mx-1";
        break;
      case "large":
        circleSize = "text-[10px]";
        spacing = "mx-1.5";
        break;
      default:
        circleSize = "text-[8px]";
        spacing = "mx-0.5";
    }

    for (let i = 0; i < flightStopCount; i++) {
      indicators.push(
        <FontAwesomeIcon
          key={i}
          icon={faCircle}
          className={`${circleSize} text-[#0C2C82] ${spacing}`}
        />
      );
    }
    return indicators;
  };


  return (
    <div className="bg-white rounded-2xl  border border-gray-100 mb-4 overflow-hidden">
      {/* Mobile Design - Small screens only */}
      <div className="block md:hidden">
        <div className="p-4">
          {/* Header - Airline Logo and Name */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-10 border border-gray-200 rounded-xl flex items-center justify-center overflow-hidden bg-white">
                {image ? (
                  <img
                    src={image}
                    alt={`${flightName} logo`}
                    className="h-6 w-auto object-contain"
                    onError={(e) => {
                      e.target.style.display = "none";
                      // Create and show fallback emoji
                      const fallback = document.createElement('div');
                      fallback.className = "text-blue-600 font-bold text-lg transform rotate-45";
                      fallback.textContent = "✈";
                      e.target.parentNode.appendChild(fallback);
                    }}
                  />
                ) : (
                  <div className="text-blue-600 font-bold text-lg transform rotate-45">
                    ✈
                  </div>
                )}
              </div>
              <div className="text-lg font-bold text-[#0C2C82]">
                {flightName}
              </div>
            </div>
          </div>

          {/* Flight Route Info */}
          <div className="flex items-center justify-between mb-6">
            {/* Departure */}
            <div className="flex gap-1">
              <div className="w-6 h-6 text-[#0C2C82] flex items-center justify-center mt-1">
                <FontAwesomeIcon
                  icon={faPlaneDeparture}
                  className="text-transparent text-lg stroke-[#0C2C82] w-4 h-4"
                  style={{ strokeWidth: 30 }}
                />
              </div>
              <div className="flex flex-col items-center justify-center">
                <div className="text-xl font-bold text-[#0C2C82]">
                  {departureTime || "07:30"}
                </div>
                <div className="text-sm font-semibold text-[#0C2C82]">
                  {departureCode}
                </div>
              </div>
            </div>

            {/* Route Line and Info */}
            <div className="flex-1 flex flex-col items-center mx-6">
              <div className="text-[9px] font-semibold text-[#0C2C82] mb-2">
                {stops}
              </div>
              <div className="w-full relative flex items-center">
                <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                {renderStops("small")}
                <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                <div className="absolute -right-1  w-0 h-0 border-l-8 border-l-[#0C2C82] border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
              </div>
              <div className="text-[9px] font-semibold text-[#0C2C82] mt-2">
                {duration}
              </div>
            </div>

            {/* Arrival */}
            <div className="flex gap-1">
              <div className="w-6 h-6 text-[#0C2C82] flex items-center justify-center mt-1">
                <FontAwesomeIcon
                  icon={faPlaneArrival}
                  className="text-transparent text-lg stroke-[#0C2C82] w-4 h-4"
                  style={{ strokeWidth: 30 }}
                />
              </div>
              <div className="flex flex-col items-center justify-center">
                <div className="text-xl font-bold text-[#0C2C82]">
                  {arrivalTime || "08:35"}
                </div>
                <div className="text-sm font-semibold text-[#0C2C82]">
                  {arrivalCode}
                </div>
              </div>
            </div>
          </div>
          {/* Return Route for Round Trip */}
          {isRoundTrip && returnFlight && (
            <div className="mt-3">
              {/* Return Route with Integrated Logo */}
              <div className="flex items-center justify-between mb-4">
                {/* Airline Logo and Name */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-10 border border-gray-200 rounded-xl flex items-center justify-center overflow-hidden bg-white">
                      {image ? (
                        <img
                          src={image}
                          alt={`${flightName} logo`}
                          className="h-6 w-auto object-contain"
                          onError={(e) => {
                            e.target.style.display = "none";
                            // Create and show fallback emoji
                            const fallback = document.createElement('div');
                            fallback.className = "text-blue-600 font-bold text-lg transform rotate-45";
                            fallback.textContent = "✈";
                            e.target.parentNode.appendChild(fallback);
                          }}
                        />
                      ) : (
                        <div className="text-blue-600 font-bold text-lg transform rotate-45">
                          ✈
                        </div>
                      )}
                    </div>
                    <div className="text-lg font-bold text-[#0C2C82]">
                      {flightName}
                    </div>
                  </div>
                </div>
              </div>

              {/* Return Route */}
              <div className="flex items-center justify-between">
                {/* Return Departure (Original Arrival) */}
                <div className="flex gap-1">
                  <div className="w-6 h-6 text-[#0C2C82] flex items-center justify-center mt-1">
                    <FontAwesomeIcon
                      icon={faPlaneDeparture}
                      className="text-transparent text-lg stroke-[#0C2C82] w-4 h-4"
                      style={{ strokeWidth: 30 }}
                    />
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-xl font-bold text-[#0C2C82]">
                      {returnFlight.departureTime || "12:00"}
                    </div>
                    <div className="text-sm font-semibold text-[#0C2C82]">
                      {arrivalCode}
                    </div>
                  </div>
                </div>

                {/* Return Route Line */}
                <div className="flex-1 flex flex-col items-center mx-6">
                  <div className="text-[9px] font-semibold text-[#0C2C82] mb-2">
                    {stops}
                  </div>
                  <div className="w-full relative flex items-center">
                    <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                    {renderStops("small")}
                    <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                    <div className="absolute -right-1  w-0 h-0 border-l-8 border-l-[#0C2C82] border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
                  </div>
                  <div className="text-[9px] font-semibold text-[#0C2C82] mt-2">
                    {duration}
                  </div>
                </div>

                {/* Return Arrival (Original Departure) */}
                <div className="flex gap-1">
                  <div className="w-6 h-6 text-[#0C2C82] flex items-center justify-center mt-1">
                    <FontAwesomeIcon
                      icon={faPlaneArrival}
                      className="text-transparent text-lg stroke-[#0C2C82] w-4 h-4"
                      style={{ strokeWidth: 30 }}
                    />
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-xl font-bold text-[#0C2C82]">
                      {returnFlight.arrivalTime || "16:00"}
                    </div>
                    <div className="text-sm font-semibold text-[#0C2C82]">
                      {departureCode}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Price and Book Button */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex flex-col">
              {discount > 0 && (
                <span className="text-sm text-gray-500 line-through">
                  ${price}
                </span>
              )}
              <span className="text-2xl font-bold text-red-500">
                ${finalPrice}
              </span>
            </div>
            <ActionButton
              className="text-lg h-10 w-auto"
              onClick={handleSelectFlight}
              text="Select"
            />
          </div>

          {/* Bottom Info */}
          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
            <div className="text-sm font-medium text-[#0C2C82]">
              {refund || "Partially Refundable"}
            </div>
            <button
              onClick={toggleDetails}
              className="flex items-center space-x-1 text-[#0C2C82] font-medium"
            >
              <span className="text-sm">Flight Details</span>
              <ChevronDownIcon
                className={`w-4 h-4 transition-transform duration-200 ${
                  showDetails ? "rotate-180" : ""
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Tablet Design - md and lg screens */}
      <div className="hidden md:block xl:hidden">
        <div className="p-4">
          {/* Header Row - Airline Info */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-10 border border-gray-200 rounded-xl flex items-center justify-center overflow-hidden bg-white">
                {image ? (
                  <img
                    src={image}
                    alt={`${flightName} logo`}
                    className="h-6 w-auto object-contain"
                    onError={(e) => {
                      e.target.style.display = "none";
                      // Create and show fallback emoji
                      const fallback = document.createElement('div');
                      fallback.className = "text-blue-600 font-bold text-lg transform rotate-45";
                      fallback.textContent = "✈";
                      e.target.parentNode.appendChild(fallback);
                    }}
                  />
                ) : (
                  <div className="text-blue-600 font-bold text-lg transform rotate-45">
                    ✈
                  </div>
                )}
              </div>
              <div className="text-xl font-bold text-[#0C2C82]">
                {flightName}
              </div>
            </div>
            <div className="text-lg font-bold text-[#0C2C82]">{duration}</div>
          </div>

          {/* Flight Route - Full Width */}
          <div className="flex items-center justify-between mb-4">
            {/* Departure */}
            <div className="flex gap-2">
              <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                <FontAwesomeIcon
                  icon={faPlaneDeparture}
                  className="text-transparent text-2xl stroke-[#0C2C82] w-6 h-6"
                  style={{ strokeWidth: 30 }}
                />
              </div>
              <div className="flex flex-col items-center justify-center">
                <div className="text-2xl font-bold text-[#0C2C82]">
                  {departureTime || "07:30"}
                </div>
                <div className="text-base font-semibold text-[#0C2C82]">
                  {departureCode}
                </div>
              </div>
            </div>

            {/* Route Line and Info - Full Width */}
            <div className="flex-1 flex flex-col items-center mx-8">
              <div className="text-sm font-semibold text-[#0C2C82] mb-2">
                {stops}
              </div>
              <div className="w-full relative flex items-center">
                <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                {renderStops("medium")}
                <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                <div className="absolute -right-1  w-0 h-0 border-l-8 border-l-[#0C2C82] border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
              </div>
            </div>

            {/* Arrival */}
            <div className="flex gap-2">
              <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                <FontAwesomeIcon
                  icon={faPlaneArrival}
                  className="text-transparent text-2xl stroke-[#0C2C82] w-6 h-6"
                  style={{ strokeWidth: 30 }}
                />
              </div>
              <div className="flex flex-col items-center justify-center">
                <div className="text-2xl font-bold text-[#0C2C82]">
                  {arrivalTime || "08:35"}
                </div>
                <div className="text-base font-semibold text-[#0C2C82]">
                  {arrivalCode}
                </div>
              </div>
            </div>
          </div>

          {/* Return Route for Round Trip - Tablet */}
          {isRoundTrip && returnFlight && (
            <div className="mb-4">
              {/* Return Route Header with Logo and Flight Info in Same Row */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-10 border border-gray-200 rounded-xl flex items-center justify-center overflow-hidden bg-white">
                    {image ? (
                      <img
                        src={image}
                        alt={`${flightName} logo`}
                        className="h-6 w-auto object-contain"
                        onError={(e) => {
                          e.target.style.display = "none";
                          // Create and show fallback emoji
                          const fallback = document.createElement('div');
                          fallback.className = "text-blue-600 font-bold text-lg transform rotate-45";
                          fallback.textContent = "✈";
                          e.target.parentNode.appendChild(fallback);
                        }}
                      />
                    ) : (
                      <div className="text-blue-600 font-bold text-lg transform rotate-45">
                        ✈
                      </div>
                    )}
                  </div>
                  <div className="text-xl font-bold text-[#0C2C82]">
                    {flightName}
                  </div>
                </div>
                <div className="text-lg font-bold text-[#0C2C82]">
                  {duration}
                </div>
              </div>

              <div className="flex items-center justify-between">
                {/* Return Departure */}
                <div className="flex gap-2">
                  <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                    <FontAwesomeIcon
                      icon={faPlaneDeparture}
                      className="text-transparent text-2xl stroke-[#0C2C82] w-6 h-6"
                      style={{ strokeWidth: 30 }}
                    />
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-2xl font-bold text-[#0C2C82]">
                      {returnFlight.departureTime || "12:00"}
                    </div>
                    <div className="text-base font-semibold text-[#0C2C82]">
                      {arrivalCode}
                    </div>
                  </div>
                </div>

                {/* Return Route Line */}
                <div className="flex-1 flex flex-col items-center mx-8">
                  <div className="text-sm font-semibold text-[#0C2C82] mb-2">
                    {stops}
                  </div>
                  <div className="w-full relative flex items-center">
                    <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                    {renderStops("medium")}
                    <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                    <div className="absolute -right-1  w-0 h-0 border-l-8 border-l-[#0C2C82] border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
                  </div>
                </div>

                {/* Return Arrival */}
                <div className="flex gap-2">
                  <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                    <FontAwesomeIcon
                      icon={faPlaneArrival}
                      className="text-transparent text-2xl stroke-[#0C2C82] w-6 h-6"
                      style={{ strokeWidth: 30 }}
                    />
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-2xl font-bold text-[#0C2C82]">
                      {returnFlight.arrivalTime || "16:00"}
                    </div>
                    <div className="text-base font-semibold text-[#0C2C82]">
                      {departureCode}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Price and Book Button Row */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex flex-col">
              {discount > 0 && (
                <span className="text-base text-gray-500 line-through">
                  ${price}
                </span>
              )}
              <span className="text-3xl font-bold text-red-500">
                ${finalPrice}
              </span>
            </div>
            <ActionButton
              className="text-lg h-10 w-auto"
              onClick={handleSelectFlight}
              text="Select"
            />
          </div>

          {/* Bottom Row - Refund and Details */}
          <div className="flex items-center justify-between pt-3 border-t border-gray-100">
            <div className="text-base font-medium text-[#0C2C82]">
              {refund || "Partially Refundable"}
            </div>
            <button
              onClick={toggleDetails}
              className="flex items-center space-x-2 text-[#0C2C82] font-medium"
            >
              <span className="text-base">Flight Details</span>
              <ChevronDownIcon
                className={`w-5 h-5 transition-transform duration-200 ${
                  showDetails ? "rotate-180" : ""
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Desktop Design - xl screens and above */}
      <div className="hidden xl:block">
        <div className="p-6">
          <div className="flex">
          <div className="flex w-full items-center justify-between pb-4">
            {/* Airline Info */}
            <div className="flex items-center space-x-4 w-[25%]">
              <div className="w-16 h-14 border border-[#EBEBEB] rounded-2xl flex items-center justify-center overflow-hidden">
                {image ? (
                  <img
                    src={image}
                    alt={`${flightName} logo`}
                    className="h-7 object-cover"
                    onError={(e) => {
                      e.target.style.display = "none";
                      e.target.nextSibling.style.display = "flex";
                    }}
                  />
                ) : null}
                <div
                  className="text-gray-500 font-bold text-sm transform rotate-45"
                  style={{ display: image ? "none" : "flex" }}
                >
                  ✈
                </div>
              </div>
              <div>
                <h3 className="text-base font-bold text-[#0C2C82]">
                  {flightName}
                </h3>
              </div>
            </div>

            {/* Desktop Flight Route */}
            <div className="flex items-center space-x-8 flex-1 max-w-md mx-8">
              {/* Departure */}
              <div className="flex gap-2">
                <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                  <FontAwesomeIcon
                    icon={faPlaneDeparture}
                    className="text-transparent text-3xl stroke-[#0C2C82] w-8 h-8"
                    style={{ strokeWidth: 30 }}
                  />
                </div>
                <div className="flex flex-col items-center justify-center">
                  <div className="text-lg font-bold text-[#0C2C82]">
                    {departureTime}
                  </div>
                  <div className="text-base font-semibold text-[#0C2C82]">
                    {departureCode}
                  </div>
                </div>
              </div>

              {/* Flight Path */}
              <div className="flex-none relative flex flex-col items-center">
                <div className="flex items-center justify-center">
                  <div className="text-lg text-center font-semibold text-[#0C2C82] px-3 py-1 rounded-full w-42">
                    {stops}
                  </div>
                </div>
                <div className="flex items-center mt-2 w-full justify-center">
                  <div className="flex-1 h-0.5 bg-[#0C2C82] mx-6 relative flex items-center">
                    <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                    {renderStops("large")}
                    <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                    <div className="absolute -right-1 xl:-top-[0.4px] w-0 h-0 border-l-8 border-l-[#0C2C82] border-t-5 border-t-transparent border-b-5 border-b-transparent transform translate-y-[-3px]"></div>
                  </div>
                </div>
              </div>

              {/* Arrival */}
              <div className="flex gap-2">
                <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                  <FontAwesomeIcon
                    icon={faPlaneArrival}
                    className="text-transparent text-3xl stroke-[#0C2C82] w-8 h-8"
                    style={{ strokeWidth: 30 }}
                  />
                </div>
                <div className="flex flex-col items-center justify-center">
                  <div className="text-lg font-bold text-[#0C2C82]">
                    {arrivalTime}
                  </div>
                  <div className="text-base font-semibold text-[#0C2C82]">
                    {arrivalCode}
                  </div>
                </div>
              </div>
            </div>

            {/* Duration */}
            <div className="text-center flex-1 font-semibold text-base text-[#0C2C82]">
              {duration}
            </div>
          </div>
          {/* Pricing and Select */}
          <div className={`flex flex-col ${isRoundTrip ? '-mb-10' : ''} justify-center items-center space-x-4 w-auto `}>
            <div className="text-right">
              <div className="mb-1">
                {discount > 0 && (
                  <span className="text-sm text-[#0C2C82] line-through mr-2">
                    ${price}
                  </span>
                )}
                <span className="text-xl font-bold text-[#F96768]">
                  ${finalPrice}
                </span>
              </div>
            </div>
            <div className="flex justify-end">
              <ActionButton
                className="text-lg h-10 w-auto"
                onClick={handleSelectFlight}
                text="Select"
              />
            </div>
          </div>
          </div>

          {/* Return Flight Route for Desktop Round Trip */}
          {isRoundTrip && returnFlight && (
            <div className="flex">
            <div className="flex w-full items-center justify-between pb-4">
              {/* Return Route Logo and Name */}
              <div className="flex items-center space-x-4 w-[25%]">
                <div className="w-16 h-14 border border-[#EBEBEB] rounded-2xl flex items-center justify-center overflow-hidden">
                  {image ? (
                    <img
                      src={image}
                      alt={`${flightName} logo`}
                      className="h-7 object-cover"
                      onError={(e) => {
                        e.target.style.display = "none";
                        e.target.nextSibling.style.display = "flex";
                      }}
                    />
                  ) : null}
                  <div
                    className="text-gray-500 font-bold text-sm transform rotate-45"
                    style={{ display: image ? "none" : "flex" }}
                  >
                    ✈
                  </div>
                </div>
                <div>
                  <h3 className="text-base font-bold text-[#0C2C82]">
                    {flightName}
                  </h3>
                </div>
              </div>

              {/* Return Route */}
              <div className="flex items-center space-x-8 flex-1 max-w-md mx-8">
                {/* Return Departure */}
                <div className="flex gap-2">
                  <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                    <FontAwesomeIcon
                      icon={faPlaneDeparture}
                      className="text-transparent text-3xl stroke-[#0C2C82] w-8 h-8"
                      style={{ strokeWidth: 30 }}
                    />
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-lg font-bold text-[#0C2C82]">
                      {returnFlight.departureTime}
                    </div>
                    <div className="text-base font-semibold text-[#0C2C82]">
                      {arrivalCode}
                    </div>
                  </div>
                </div>

                {/* Return Flight Path */}
                <div className="flex-none relative flex flex-col items-center">
                  <div className="flex items-center justify-center">
                    <div className="text-lg text-center font-semibold text-[#0C2C82] px-3 py-1 rounded-full w-42">
                      {returnFlight.stops}
                    </div>
                  </div>
                  <div className="flex items-center mt-2 w-full justify-center">
                    <div className="flex-1 h-0.5 bg-[#0C2C82] mx-6 relative flex items-center">
                      <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                      {(() => {
                        const returnStopCount = getStopCount(
                          returnFlight.stops
                        );
                        const indicators = [];
                        for (let i = 0; i < returnStopCount; i++) {
                          indicators.push(
                            <FontAwesomeIcon
                              key={i}
                              icon={faCircle}
                              className="text-[10px] text-[#0C2C82] mx-1.5"
                            />
                          );
                        }
                        return indicators;
                      })()}
                      <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                      <div className="absolute -right-1 xl:-top-[0.4px] w-0 h-0 border-l-8 border-l-[#0C2C82] border-t-5 border-t-transparent border-b-5 border-b-transparent transform translate-y-[-3px]"></div>
                    </div>
                  </div>
                </div>

                {/* Return Arrival */}
                <div className="flex gap-2">
                  <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                    <FontAwesomeIcon
                      icon={faPlaneArrival}
                      className="text-transparent text-3xl stroke-[#0C2C82] w-8 h-8"
                      style={{ strokeWidth: 30 }}
                    />
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-lg font-bold text-[#0C2C82]">
                      {returnFlight.arrivalTime}
                    </div>
                    <div className="text-base font-semibold text-[#0C2C82]">
                      {departureCode}
                    </div>
                  </div>
                </div>
              </div>
              {/* Return Duration */}
              <div className="text-center flex-1 font-semibold text-base text-[#0C2C82]">
                {returnFlight.duration}
              </div>

              
            </div>
            {/* Spacer to align with pricing */}
              <div className="w-[15.5%]">
              </div>
              </div>
          )}

          {/* Desktop Flight Details Section */}
          <div className="flex justify-between items-center mt-2 pt-2 border-t border-gray-200">
            <div className="flex justify-start space-x-2">
              {refund && (
                <span className="text-sm text-[#0C2C82]">{refund}</span>
              )}
            </div>
            <div className="flex justify-end">
              <button
                onClick={toggleDetails}
                className="flex items-center space-x-2 text-[#0C2C82] hover:text-[#24BDC7] font-medium transition-colors"
              >
                <span className="text-base">Flight Details</span>
                <ChevronDownIcon
                  className={`w-5 h-5 transition-transform duration-200 ${
                    showDetails ? "rotate-180" : ""
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Expanded Details */}
      {showDetails && (
        <div className="border-t flex flex-col lg:flex-row border-gray-200">
          {/* Route Info - 60% width */}
          <div className="p-6 border-b lg:border-b-0  border-gray-200 lg:w-3/5">
            {/* Flight Switching Tabs (For Round Trip and Multi-City) */}
            {(isRoundTrip || isMultiCity) && (
              <div className="flex border-b border-gray-100 mb-4">
                {/* Outbound/First Segment Tab */}
                <button
                  onClick={() => setActiveFlightTab("outbound")}
                  className={`pb-2 px-1 font-medium text-sm text-center ${
                    activeFlightTab === "outbound"
                      ? "text-[#009689] border-b-2 border-[#009689]"
                      : "text-[#0C2C82] hover:text-gray-800"
                  } ${isMultiCity ? 'w-[25%]' : 'w-[20%]'}`}
                >
                  {departureCode} - {arrivalCode}
                </button>

                {/* Return Tab (Round Trip) */}
                {isRoundTrip && (
                  <button
                    onClick={() => setActiveFlightTab("return")}
                    className={`w-[20%] pb-2 px-1 font-medium text-sm text-center ${
                      activeFlightTab === "return"
                        ? "text-[#009689] border-b-2 border-[#009689]"
                        : "text-[#0C2C82] hover:text-gray-800"
                    }`}
                  >
                    {arrivalCode} - {departureCode}
                  </button>
                )}

                {/* Multi-City Segment Tabs */}
                {isMultiCity && multiCityFlights.map((segment, index) => (
                  <button
                    key={segment.id}
                    onClick={() => setActiveFlightTab(`segment-${index + 1}`)}
                    className={`w-[25%] pb-2 px-1 font-medium text-sm text-center ${
                      activeFlightTab === `segment-${index + 1}`
                        ? "text-[#009689] border-b-2 border-[#009689]"
                        : "text-[#0C2C82] hover:text-gray-800"
                    }`}
                  >
                    {segment.departureCode} - {segment.arrivalCode}
                  </button>
                ))}
              </div>
            )}


            {/* Airline Details */}
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-14 h-10 rounded-lg border-1 border-[#E5E7EB] flex items-center justify-center overflow-hidden">
                {(() => {
                  let currentImage = image;
                  let currentFlightName = flightName;

                  if (isRoundTrip && activeFlightTab === "return") {
                    currentImage = returnFlight?.image || image;
                    currentFlightName = flightName;
                  } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                    const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                    const segment = multiCityFlights[segmentIndex];
                    currentImage = segment?.image || image;
                    currentFlightName = segment?.flightName || flightName;
                  }

                  return currentImage ? (
                    <img
                      src={currentImage}
                      alt={`${currentFlightName} logo`}
                      className="h-7 object-cover"
                      onError={(e) => {
                        e.target.style.display = "none";
                        e.target.nextSibling.style.display = "flex";
                      }}
                    />
                  ) : null;
                })()}
                <div
                  className="text-[#0C2C82] font-bold text-xs transform rotate-45"
                  style={{
                    display: (() => {
                      let currentImage = image;
                      if (isRoundTrip && activeFlightTab === "return") {
                        currentImage = returnFlight?.image || image;
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        currentImage = segment?.image || image;
                      }
                      return currentImage ? "none" : "flex";
                    })()
                  }}
                >
                  ✈
                </div>
              </div>
              <div>
                <div className="font-semibold text-[#0C2C82]">
                  {(() => {
                    if (isRoundTrip && activeFlightTab === "return") {
                      return flightName;
                    } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                      const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                      const segment = multiCityFlights[segmentIndex];
                      return segment?.flightName || flightName;
                    }
                    return flightName;
                  })()}
                </div>
                <div className="text-sm text-[#0C2C82]">
                  Flight {(() => {
                    if (isRoundTrip && activeFlightTab === "return") {
                      return id;
                    } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                      const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                      const segment = multiCityFlights[segmentIndex];
                      return segment?.id || id;
                    }
                    return id;
                  })()}
                </div>
              </div>
            </div>

            {/* Flight Route - Same as Desktop Design */}
            <div className="flex items-center justify-between mb-4">
              {/* Departure */}
              <div className="flex gap-2">
                <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                  <FontAwesomeIcon
                    icon={faPlaneDeparture}
                    className="text-transparent text-2xl stroke-[#0C2C82] w-6 h-6"
                    style={{ strokeWidth: 30 }}
                  />
                </div>
                <div className="flex flex-col items-center justify-center">
                  <div className="text-lg md:text-xl font-bold text-[#0C2C82]">
                    {(() => {
                      if (isRoundTrip && activeFlightTab === "return") {
                        return returnFlight?.departureTime || "16:45";
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        return segment?.departureTime || "10:00";
                      }
                      return departureTime || "10:00";
                    })()}
                  </div>
                  <div className="text-xs md:text-sm text-[#0C2C82]">
                    {(() => {
                      if (isRoundTrip && activeFlightTab === "return") {
                        return returnFlight?.departureDate || "Tomorrow";
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        return segment?.departureDate || "Today";
                      }
                      return departureDate || "Today";
                    })()}
                  </div>
                  <div className="font-semibold text-[#0C2C82] text-sm md:text-base">
                    {(() => {
                      if (isRoundTrip && activeFlightTab === "return") {
                        return arrivalCode;
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        return segment?.departureCode || departureCode;
                      }
                      return departureCode;
                    })()}
                  </div>
                  <div className="text-xs text-[#0C2C82]">
                    {(() => {
                      if (isRoundTrip && activeFlightTab === "return") {
                        return arrivalCity;
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        return segment?.departureCity || departureCity;
                      }
                      return departureCity;
                    })()}
                  </div>
                </div>
              </div>

              {/* Route Line and Info - Full Width */}
              <div className="flex-1 flex flex-col items-center mx-8">
                <div className="text-[9px] md:text-sm font-semibold text-[#0C2C82] mb-2">
                  {(() => {
                    if (isRoundTrip && activeFlightTab === "return") {
                      return returnFlight?.stops || "Direct";
                    } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                      const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                      const segment = multiCityFlights[segmentIndex];
                      return segment?.stops || "Direct";
                    }
                    return stops;
                  })()}
                </div>
                <div className="w-full relative flex items-center">
                  <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                  {(() => {
                    if (isRoundTrip && activeFlightTab === "return") {
                      return renderStopsForFlight(returnFlight, "medium");
                    } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                      const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                      const segment = multiCityFlights[segmentIndex];
                      return renderStopsForFlight(segment, "medium");
                    }
                    return renderStops("medium");
                  })()}
                  <div className="flex-1 h-0.5 bg-[#0C2C82]"></div>
                  <div className="absolute -right-1  w-0 h-0 border-l-8 border-l-[#0C2C82] border-t-5 border-t-transparent border-b-5 border-b-transparent"></div>
                </div>
                <div className="text-xs md:text-sm text-[#0C2C82] mt-2">
                  {(() => {
                    if (isRoundTrip && activeFlightTab === "return") {
                      return returnFlight?.duration || "4h 30m";
                    } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                      const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                      const segment = multiCityFlights[segmentIndex];
                      return segment?.duration || "2h 15m";
                    }
                    return duration;
                  })()}
                </div>
              </div>

              {/* Arrival */}
              <div className="flex gap-2">
                <div className="w-8 h-8 text-[#0C2C82] flex items-center justify-center mt-2">
                  <FontAwesomeIcon
                    icon={faPlaneArrival}
                    className="text-transparent text-2xl stroke-[#0C2C82] w-6 h-6"
                    style={{ strokeWidth: 30 }}
                  />
                </div>
                <div className="flex flex-col items-center justify-center">
                  <div className="text-lg md:text-xl font-bold text-[#0C2C82]">
                    {(() => {
                      if (isRoundTrip && activeFlightTab === "return") {
                        return returnFlight?.arrivalTime || "21:15";
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        return segment?.arrivalTime || "14:30";
                      }
                      return arrivalTime || "14:30";
                    })()}
                  </div>
                  <div className="text-xs md:text-sm text-[#0C2C82]">
                    {(() => {
                      if (isRoundTrip && activeFlightTab === "return") {
                        return returnFlight?.arrivalDate || "Tomorrow";
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        return segment?.arrivalDate || "Today";
                      }
                      return arrivalDate || "Today";
                    })()}
                  </div>
                  <div className="font-semibold text-[#0C2C82] text-sm md:text-base">
                    {(() => {
                      if (isRoundTrip && activeFlightTab === "return") {
                        return departureCode;
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        return segment?.arrivalCode || arrivalCode;
                      }
                      return arrivalCode;
                    })()}
                  </div>
                  <div className="text-xs text-[#0C2C82]">
                    {(() => {
                      if (isRoundTrip && activeFlightTab === "return") {
                        return departureCity;
                      } else if (isMultiCity && activeFlightTab.startsWith("segment-")) {
                        const segmentIndex = parseInt(activeFlightTab.split("-")[1]) - 1;
                        const segment = multiCityFlights[segmentIndex];
                        return segment?.arrivalCity || arrivalCity;
                      }
                      return arrivalCity;
                    })()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs - 40% width */}
          <div className="p-4 lg:p-6 lg:w-2/5">
            {/* Tabs Navigation */}  
            <div className="flex border-b border-gray-200 mb-4">
              <button
                onClick={() => setActiveTab("baggage")}
                className={`w-1/3 pb-2 px-1 font-medium text-sm text-center ${
                  activeTab === "baggage"
                    ? "text-teal-600 border-b-2 border-teal-600"
                    : "text-[#0C2C82] hover:text-gray-800"
                }`}
              >
                Baggage
              </button>
              <button
                onClick={() => setActiveTab("fare")}
                className={`w-1/3 pb-2 px-1 font-medium text-sm text-center ${
                  activeTab === "fare"
                    ? "text-teal-600 border-b-2 border-teal-600"
                    : "text-[#0C2C82] hover:text-gray-800"
                }`}
              >
                Fare
              </button>
              <button
                onClick={() => setActiveTab("policy")}
                className={`w-1/3 pb-2 px-1 font-medium text-sm text-center ${
                  activeTab === "policy"
                    ? "text-teal-600 border-b-2 border-teal-600"
                    : "text-[#0C2C82] hover:text-gray-800"
                }`}
              >
                Policy
              </button>
            </div>

            {/* Tab Content */}
            {activeTab === "baggage" && (
              <div className="space-y-4 text-[#0C2C82]">
                <div className="flex justify-between items-center  pb-2">
                  <div className="font-semibold text-sm">Flight</div>
                  <div className="text-sm">
                    {isRoundTrip && activeFlightTab === "return" 
                      ? `${arrivalCode} - ${departureCode}` 
                      : `${departureCode} - ${arrivalCode}`}
                  </div>
                </div>
                <div className="flex justify-between items-center  pb-2">
                  <div className="font-semibold text-sm">Cabin</div>
                  <div className="text-sm font-medium">
                    {isRoundTrip && activeFlightTab === "return" 
                      ? returnFlight?.baggage?.cabin || baggage.cabin
                      : baggage.cabin}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="font-semibold text-sm">Checked In</div>
                  <div className="text-sm font-medium">
                    {isRoundTrip && activeFlightTab === "return" 
                      ? returnFlight?.baggage?.checkIn || baggage.checkIn
                      : baggage.checkIn}
                  </div>
                </div>
              </div>
            )}

            {activeTab === "fare" && (
              <div className="text-[#0C2C82] space-y-3">
                {/* Header */}
                <div className="text-sm font-bold  pb-2">
                  {isRoundTrip && activeFlightTab === "return" 
                    ? "Return Flight Fare Summary" 
                    : "Fare Summary"}
                </div>

                {/* Adult Row */}
                <div className="flex justify-between items-center text-xs">
                  <div>Adult x 1</div>
                  <div className="text-right">
                    <div className="font-medium">
                      ${isRoundTrip && activeFlightTab === "return" 
                        ? returnFlight?.fare?.adult || "5,423"
                        : "5,423"}
                    </div>
                    <div className="text-gray-500">+ $1,000 tax</div>
                  </div>
                </div>

                {/* Child Row */}
                <div className="flex justify-between items-center text-xs">
                  <div>Child x 1</div>
                  <div className="text-right">
                    <div className="font-medium">
                      ${isRoundTrip && activeFlightTab === "return" 
                        ? returnFlight?.fare?.child || "3,423"
                        : "3,423"}
                    </div>
                    <div className="text-gray-500">+ $1,000 tax</div>
                  </div>
                </div>

                {/* Total Row */}
                <div className="bg-[#EEFAFB] rounded-lg p-3 mt-4">
                  <div className="flex justify-between items-center">
                    <div className="text-sm font-semibold">
                      Total (1 Traveler)
                    </div>
                    <div className="text-lg font-bold text-red-500">
                      $
                      {isRoundTrip && activeFlightTab === "return" 
                        ? (returnFlight?.price || Math.round(price * 0.8))
                        : (discount > 0
                          ? Math.round(price * (1 - discount / 100))
                          : price)}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "policy" && (
              <div className="space-y-3 text-[#0C2C82]">
                {/* Flight Route Info */}
                <div className="text-sm font-semibold mb-3">
                  {isRoundTrip && activeFlightTab === "return" 
                    ? `Return Flight Policy (${arrivalCode} - ${departureCode})` 
                    : `Flight Policy (${departureCode} - ${arrivalCode})`}
                </div>
                
                <div className="space-y-2 text-xs leading-relaxed">
                  <div className="flex">
                    <span className="text-teal-600 mr-2">•</span>
                    <span>
                      Refund and Date Change are done as per airline policies.
                    </span>
                  </div>
                  <div className="flex">
                    <span className="text-teal-600 mr-2">•</span>
                    <span>
                      Refund Charge includes airline policy + convenience fee.
                    </span>
                  </div>
                  <div className="flex">
                    <span className="text-teal-600 mr-2">•</span>
                    <span>
                      Date Change Fee includes airline policy + convenience fee.
                    </span>
                  </div>
                  <div className="flex">
                    <span className="text-teal-600 mr-2">•</span>
                    <span>No refund for no-show passengers.</span>
                  </div>
                  {isRoundTrip && activeFlightTab === "return" && (
                    <div className="flex">
                      <span className="text-teal-600 mr-2">•</span>
                      <span>Return flight changes may affect outbound flight validity.</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FlightCard;
