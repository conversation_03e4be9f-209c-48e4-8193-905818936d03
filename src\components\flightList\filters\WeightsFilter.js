import React from 'react';
import FilterSection from './FilterSection';
import CheckboxItem from './CheckboxItem';

const WeightsFilter = ({ filters, onFilterChange }) => {
  const weightOptions = [
    { label: '25 kg', count: '20', value: '25' }
  ];

  return (
    <FilterSection title="Weights">
      {weightOptions.map((weight) => (
        <CheckboxItem
          key={weight.value}
          label={weight.label}
          count={weight.count}
          checked={filters.weights?.includes(weight.value)}
          onChange={(e) => onFilterChange('weights', weight.value, e.target.checked)}
        />
      ))}
    </FilterSection>
  );
};

export default WeightsFilter;
