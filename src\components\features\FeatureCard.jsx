import React from 'react';
import Image from 'next/image';

const FeatureCard = ({ feature, index, isMobile = false }) => {
  return (
    <div
      className={`bg-white dark:bg-gray-800 ${
        isMobile 
          ? "rounded-xl sm:rounded-2xl p-4 sm:p-6" 
          : "rounded-2xl p-6"
      } shadow-md hover:shadow-lg transition-all duration-300`}
      style={{ animationDelay: `${index * (isMobile ? 0.1 : 0.2)}s` }}
    >
      <div className="flex items-start space-x-4">
        {/* Icon */}
        <div className={`flex-shrink-0 ${
          isMobile 
            ? "w-12 h-12 sm:w-14 sm:h-14" 
            : "w-14 h-14"
        } bg-[#24BDC7] rounded-full rounded-bl-none flex items-center justify-center`}>
          <Image
            src={feature.icon}
            alt={feature.title}
            width={isMobile ? 24 : 28}
            height={isMobile ? 24 : 28}
            className={`${
              isMobile 
                ? "w-6 h-6 sm:w-7 sm:h-7" 
                : "w-7 h-7"
            } object-contain`}
            style={{ filter: "brightness(0) invert(1)" }}
          />
        </div>

        {/* Content */}
        <div className="flex-1">
          <h3 className={`${
            isMobile 
              ? "text-base sm:text-lg" 
              : "text-lg"
          } font-bold text-[#0C2C7A] dark:text-white ${
            isMobile ? "mb-2 sm:mb-3" : "mb-2"
          }`}>
            {feature.title}
          </h3>
          <p className="text-[#757F95] dark:text-gray-300 text-sm leading-relaxed">
            {feature.description}
          </p>
        </div>
      </div>
    </div>
  );
};

export default FeatureCard;
