"use client";
import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';

const Breadcrumb = ({ items = [] }) => {
  // Default breadcrumb with Home
  const breadcrumbItems = [
    { label: 'Home', href: '/', icon: Home },
    ...items
  ];

  return (
    <nav className="flex items-center space-x-2 text-xs sm:text-sm md:text-base lg:text-lg">
      {breadcrumbItems.map((item, index) => {
        const isLast = index === breadcrumbItems.length - 1;
        const IconComponent = item.icon;

        return (
          <React.Fragment key={index}>
            {index > 0 && (
              <ChevronRight
                size={16}
                className="text-white/70 flex-shrink-0"
              />
            )}

            <div className="flex items-center">
              {IconComponent && index === 0 && (
                <IconComponent
                  size={16}
                  className="mr-1 text-white/90"
                />
              )}

              {isLast ? (
                <span className="text-white font-medium">
                  {item.label}
                </span>
              ) : (
                <Link
                  href={item.href}
                  className="text-white/80 hover:text-white transition-colors duration-200"
                >
                  {item.label}
                </Link>
              )}
            </div>
          </React.Fragment>
        );
      })}
    </nav>
  );
};

export default Breadcrumb;
