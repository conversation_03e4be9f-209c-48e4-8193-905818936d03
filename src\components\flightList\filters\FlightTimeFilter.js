import React from 'react';
import { faSun, faMoon } from '@fortawesome/free-solid-svg-icons';
import FilterSection from './FilterSection';
import CheckboxItem from './CheckboxItem';

const FlightTimeFilter = ({ filters, onFilterChange }) => {
  const timeSlots = [
    { 
      label: '00:00 - 05:59', 
      count: '20', 
      value: 'early-morning', 
      icon: faSun 
    },
    { 
      label: '06:00 - 11:59', 
      count: '15', 
      value: 'morning', 
      icon: faSun 
    },
    { 
      label: '12:00 - 17:59', 
      count: '18', 
      value: 'afternoon', 
      icon: faSun 
    },
    { 
      label: '18:00 - 23:59', 
      count: '21', 
      value: 'evening', 
      icon: faMoon 
    }
  ];

  return (
    <FilterSection title="Flight Time">
      {timeSlots.map((slot) => (
        <div key={slot.value} className="bg-[#EEFAFB] p-2 rounded-xl">
          <CheckboxItem
            label={slot.label}
            count={slot.count}
            checked={filters.timeSlots?.includes(slot.value)}
            onChange={(e) => onFilterChange('timeSlots', slot.value, e.target.checked)}
            icon={slot.icon}
          />
        </div>
      ))}
    </FilterSection>
  );
};

export default FlightTimeFilter;
