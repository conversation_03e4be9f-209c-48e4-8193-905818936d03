"use client";
import React from 'react';

const PaymentMethodCard = ({ 
  method, 
  isSelected, 
  onSelect, 
  icon, 
  title, 
  subtitle 
}) => {
  return (
    <div 
      className={`border-2 rounded-lg p-4 text-center cursor-pointer transition-all duration-200 ${
        isSelected 
          ? 'border-[#24BDC7] bg-[#24BDC7]/5' 
          : 'border-gray-200 hover:border-[#24BDC7]/50'
      }`}
      onClick={() => onSelect(method)}
    >
      <div className="flex items-center justify-center mb-2">
        {icon}
      </div>
      <p className="text-sm font-medium text-[#0C2C7A]">{title}</p>
      {subtitle && (
        <p className="text-xs text-gray-600 mt-1">{subtitle}</p>
      )}
    </div>
  );
};

export default PaymentMethodCard;
