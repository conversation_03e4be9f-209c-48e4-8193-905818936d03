import { useState, useEffect } from 'react';

/**
 * Custom hook for fetching flight detail data
 * @param {string} flightId - The flight ID to fetch details for
 * @param {string} endpoint - The API endpoint (optional)
 * @returns {Object} - { data, loading, error, refetch }
 */
export const useFlightDetail = (flightId, endpoint = '/api/flight-detail') => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchFlightDetail = async () => {
    if (!flightId) {
      setError('Flight ID is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Construct the API URL
      const apiUrl = `${endpoint}/${flightId}`;
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add any authentication headers here if needed
          // 'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const flightData = await response.json();
      
      // Validate the response structure
      if (!flightData || !flightData.segments || flightData.segments.length === 0) {
        throw new Error('Invalid flight data structure received from API');
      }

      setData(flightData);
    } catch (err) {
      console.error('Error fetching flight detail:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFlightDetail();
  }, [flightId, endpoint]);

  const refetch = () => {
    fetchFlightDetail();
  };

  return {
    data,
    loading,
    error,
    refetch
  };
};

/**
 * Alternative hook for when you already have the API data
 * and just want to transform it for the component
 * @param {Object} apiData - Raw API data
 * @returns {Object} - Transformed data ready for FlightDetailPage component
 */
export const useFlightDataTransform = (apiData) => {
  const [transformedData, setTransformedData] = useState(null);

  useEffect(() => {
    if (!apiData) {
      setTransformedData(null);
      return;
    }

    // You can add additional transformation logic here if needed
    // For now, we'll just pass the raw API data to the component
    // since the component handles the transformation internally
    setTransformedData(apiData);
  }, [apiData]);

  return transformedData;
};

export default useFlightDetail;
