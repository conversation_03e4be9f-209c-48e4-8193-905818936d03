// components/home/<USER>/TabButton.jsx
import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const TabButton = ({
  id,
  label,
  icon,
  isActive,
  onClick
}) => {
  return (
    <button
      onClick={() => onClick(id)}
      className={`flex items-center gap-2 px-3 py-2 rounded-full font-medium transition-all text-sm whitespace-nowrap ${
        isActive
          ? 'bg-[#24BDC7] text-white shadow-lg'
          : 'bg-[#E4F7F8] text-gray-600 hover:bg-[#24BDC7] hover:text-white'
      }`}
    >
      <FontAwesomeIcon icon={icon} className="w-4 h-4" />
      {label}
    </button>
  );
};

export default TabButton;
