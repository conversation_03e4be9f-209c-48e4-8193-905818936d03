"use client"
import React, { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { ArrowLeft, User, CreditCard, Shield, CheckCircle } from 'lucide-react';
import ActionButton from '../ui/ActionButton';

export default function FlightBooking() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const flightId = searchParams.get('id');

  // Mock flight data
  const flightData = {
    id: flightId || '1',
    airline: 'Emirates',
    flightNumber: 'EK 203',
    from: 'New York (JFK)',
    to: 'Dubai (DXB)',
    departureTime: '10:30 AM',
    arrivalTime: '6:45 AM',
    duration: '14h 15m',
    price: 1250,
    date: '2025-09-15'
  };

  const [passengerInfo, setPassengerInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    passportNumber: '',
    passportExpiry: ''
  });

  const [paymentInfo, setPaymentInfo] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardHolderName: ''
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [bookingComplete, setBookingComplete] = useState(false);

  const handlePassengerChange = (e) => {
    setPassengerInfo({
      ...passengerInfo,
      [e.target.name]: e.target.value
    });
  };

  const handlePaymentChange = (e) => {
    setPaymentInfo({
      ...paymentInfo,
      [e.target.name]: e.target.value
    });
  };

  const handleNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleBookingSubmit = () => {
    // Mock booking submission
    setBookingComplete(true);
  };

  const handleBackToDetails = () => {
    router.push(`/flight-details?id=${flightData.id}`);
  };

  const handleBackToList = () => {
    router.push('/flight-list');
  };

  if (bookingComplete) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="bg-green-50 border border-green-200 rounded-lg p-8 mb-6">
            <CheckCircle className="text-green-500 mx-auto mb-4" size={64} />
            <h2 className="text-2xl font-bold text-green-800 mb-2">Booking Confirmed!</h2>
            <p className="text-green-600 mb-4">Your flight has been successfully booked.</p>
            <div className="bg-white p-4 rounded-lg mb-4">
              <p className="text-sm text-gray-600">Booking Reference: <span className="font-mono font-bold">FL{Date.now()}</span></p>
            </div>
          </div>
          <ActionButton
            onClick={handleBackToList}
            className="bg-[#24BDC7] hover:bg-[#1da8b3] text-white px-6 py-3 rounded-lg"
          >
            Back to Flight List
          </ActionButton>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-7xl mx-auto">
        {/* Back Button */}
        <button
          onClick={handleBackToDetails}
          className="flex items-center gap-2 text-[#24BDC7] hover:text-[#1da8b3] mb-6 transition-colors"
        >
          <ArrowLeft size={20} />
          Back to Flight Details
        </button>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-8">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${
                step <= currentStep ? 'bg-[#24BDC7] text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                {step}
              </div>
              {step < 3 && (
                <div className={`w-16 h-1 mx-2 ${
                  step < currentStep ? 'bg-[#24BDC7]' : 'bg-gray-200'
                }`}></div>
              )}
            </div>
          ))}
        </div>

        {/* Step Labels */}
        <div className="flex justify-center mb-8">
          <div className="flex text-sm">
            <span className={`px-4 ${currentStep >= 1 ? 'text-[#24BDC7] font-semibold' : 'text-gray-500'}`}>Passenger Info</span>
            <span className={`px-4 ${currentStep >= 2 ? 'text-[#24BDC7] font-semibold' : 'text-gray-500'}`}>Payment</span>
            <span className={`px-4 ${currentStep >= 3 ? 'text-[#24BDC7] font-semibold' : 'text-gray-500'}`}>Confirmation</span>
          </div>
        </div>

        {/* Flight Summary */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Flight Summary</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{flightData.airline} {flightData.flightNumber}</p>
              <p className="text-sm text-gray-600">{flightData.from} → {flightData.to}</p>
              <p className="text-sm text-gray-600">{flightData.date} • {flightData.departureTime}</p>
            </div>
            <div className="text-right">
              <p className="text-xl font-bold text-[#24BDC7]">${flightData.price}</p>
              <p className="text-sm text-gray-600">per person</p>
            </div>
          </div>
        </div>

        {/* Step Content */}
        {currentStep === 1 && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <User size={20} />
              Passenger Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                <input
                  type="text"
                  name="firstName"
                  value={passengerInfo.firstName}
                  onChange={handlePassengerChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <input
                  type="text"
                  name="lastName"
                  value={passengerInfo.lastName}
                  onChange={handlePassengerChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  type="email"
                  name="email"
                  value={passengerInfo.email}
                  onChange={handlePassengerChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                <input
                  type="tel"
                  name="phone"
                  value={passengerInfo.phone}
                  onChange={handlePassengerChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                <input
                  type="date"
                  name="dateOfBirth"
                  value={passengerInfo.dateOfBirth}
                  onChange={handlePassengerChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Passport Number</label>
                <input
                  type="text"
                  name="passportNumber"
                  value={passengerInfo.passportNumber}
                  onChange={handlePassengerChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
            </div>
          </div>
        )}

        {currentStep === 2 && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <CreditCard size={20} />
              Payment Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Card Number</label>
                <input
                  type="text"
                  name="cardNumber"
                  value={paymentInfo.cardNumber}
                  onChange={handlePaymentChange}
                  placeholder="1234 5678 9012 3456"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                <input
                  type="text"
                  name="expiryDate"
                  value={paymentInfo.expiryDate}
                  onChange={handlePaymentChange}
                  placeholder="MM/YY"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">CVV</label>
                <input
                  type="text"
                  name="cvv"
                  value={paymentInfo.cvv}
                  onChange={handlePaymentChange}
                  placeholder="123"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Card Holder Name</label>
                <input
                  type="text"
                  name="cardHolderName"
                  value={paymentInfo.cardHolderName}
                  onChange={handlePaymentChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
                  required
                />
              </div>
            </div>
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Shield size={16} />
                Your payment information is secure and encrypted
              </div>
            </div>
          </div>
        )}

        {currentStep === 3 && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Booking Confirmation</h3>
            <div className="space-y-4">
              <div className="border-b pb-4">
                <h4 className="font-medium mb-2">Passenger Details</h4>
                <p className="text-sm text-gray-600">{passengerInfo.firstName} {passengerInfo.lastName}</p>
                <p className="text-sm text-gray-600">{passengerInfo.email}</p>
                <p className="text-sm text-gray-600">{passengerInfo.phone}</p>
              </div>
              <div className="border-b pb-4">
                <h4 className="font-medium mb-2">Flight Details</h4>
                <p className="text-sm text-gray-600">{flightData.airline} {flightData.flightNumber}</p>
                <p className="text-sm text-gray-600">{flightData.from} → {flightData.to}</p>
                <p className="text-sm text-gray-600">{flightData.date} • {flightData.departureTime}</p>
              </div>
              <div className="flex justify-between items-center text-lg font-bold">
                <span>Total Amount:</span>
                <span className="text-[#24BDC7]">${flightData.price}</span>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-6">
          {currentStep > 1 && (
            <ActionButton
              onClick={handlePrevStep}
              className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg"
            >
              Previous
            </ActionButton>
          )}

          {currentStep < 3 ? (
            <ActionButton
              onClick={handleNextStep}
              className="bg-[#24BDC7] hover:bg-[#1da8b3] text-white px-6 py-3 rounded-lg ml-auto"
            >
              Next
            </ActionButton>
          ) : (
            <ActionButton
              onClick={handleBookingSubmit}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg ml-auto"
            >
              Confirm Booking
            </ActionButton>
          )}
        </div>
      </div>
    </div>
  );
}
