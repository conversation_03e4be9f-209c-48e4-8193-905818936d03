"use client";
import React from 'react';

const FormField = ({ 
  label, 
  name, 
  type = "text", 
  value, 
  onChange, 
  placeholder, 
  icon: Icon, 
  required = false,
  options = [],
  rows = 4,
  className = "",
  error = ""
}) => {
  const baseInputClasses = `w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent transition-colors ${className}`;
  const iconInputClasses = `w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent transition-colors ${className}`;

  const renderInput = () => {
    switch (type) {
      case 'select':
        return Icon ? (
          <div className="relative">
            <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 z-10" size={20} />
            <select
              name={name}
              value={value}
              onChange={onChange}
              className={iconInputClasses}
              required={required}
            >
              <option value="">{placeholder || `Select ${label}`}</option>
              {options.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ) : (
          <select
            name={name}
            value={value}
            onChange={onChange}
            className={baseInputClasses}
            required={required}
          >
            <option value="">{placeholder || `Select ${label}`}</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'textarea':
        return Icon ? (
          <div className="relative">
            <Icon className="absolute left-3 top-3 text-gray-400 z-10" size={20} />
            <textarea
              name={name}
              value={value}
              onChange={onChange}
              placeholder={placeholder}
              rows={rows}
              className={iconInputClasses}
              required={required}
            />
          </div>
        ) : (
          <textarea
            name={name}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            rows={rows}
            className={baseInputClasses}
            required={required}
          />
        );

      default:
        return Icon ? (
          <div className="relative">
            <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type={type}
              name={name}
              value={value}
              onChange={onChange}
              placeholder={placeholder}
              className={iconInputClasses}
              required={required}
            />
          </div>
        ) : (
          <input
            type={type}
            name={name}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            className={baseInputClasses}
            required={required}
          />
        );
    }
  };

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {renderInput()}
      {error && (
        <p className="text-red-500 text-sm">{error}</p>
      )}
    </div>
  );
};

export default FormField;
