import React from 'react';
import SectionBadge from '../ui/SectionBadge';
import appDownloadData from '../../app/data/appDownloadData'; // Adjust path as per project structure

const AppDownloadSection = () => {
  const { label, heading, description, features, appStoreImages } = appDownloadData;

  return (
    <section className="app-download-section relative py-5 sm:py-16 md:py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-10 lg:gap-12">
          {/* Left Image - Responsive and centered */}
          <div className="app-image bg-sky-100 rounded-bl-lg rounded-t-full rounded-br-full py-4 sm:py-6 md:py-8 flex-1 flex justify-center overflow-hidden order-2 lg:order-1">
            <img
              src="/assets/img/download/01.png"
              alt="Tavelo App on Phone"
              className="w-full max-w-[250px] sm:max-w-[300px] md:max-w-[400px] object-cover"
            />
          </div>

          {/* Right Content - Structured for better readability */}
          <div className="app-content flex-1 flex flex-col justify-center order-1 lg:order-2 text-center lg:text-left">

            <SectionBadge text={label} isMobile={false} />
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 text-[#0C2C7A]">
              {heading}
            </h2>

            <p className="text-sm sm:text-base text-gray-600 mb-6 sm:mb-8 max-w-[480px] mx-auto lg:mx-0">
              {description}
            </p>

            <ul className="list-none p-0 mb-6 sm:mb-8 max-w-[480px] mx-auto lg:mx-0">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center mb-3 text-gray-700 text-sm sm:text-base p-2 justify-center lg:justify-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    className="text-white mr-2.5 bg-[#24BDC7] rounded-full shadow-lg flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5"
                    aria-hidden="true"
                  >
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                  </svg>
                  <span className="ml-2">{feature}</span>
                </li>
              ))}
            </ul>

            {/* App Store Buttons - Centered and responsive */}
            <div className="app-store-buttons flex flex-col sm:flex-row gap-3 sm:gap-5 justify-center lg:justify-start items-center">
              {appStoreImages.map((store, index) => (
                <img
                  key={index}
                  src={store.src}
                  alt={store.alt}
                  className="h-10 sm:h-12 w-auto cursor-pointer transition-transform hover:scale-105"
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppDownloadSection;