'use client';
import React, { useState, useEffect } from 'react';
import Modal from '@/components/ui/Modal';
import LoginForm from './LoginForm';
import SignupForm from './SignupForm';

const AuthModal = ({
  isOpen,
  onClose,
  initialMode = 'login' 
}) => {
  const [mode, setMode] = useState(initialMode);

  // Update mode when initialMode prop changes
  useEffect(() => {
    if (isOpen) {
      setMode(initialMode);
    }
  }, [initialMode, isOpen]);

  const handleSwitchMode = (newMode) => {
    setMode(newMode);
  };

  const handleClose = () => {
    onClose();
  };

  const handleAuthSuccess = () => {
    handleClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="md"
      showCloseButton={true}
      closeOnBackdropClick={true}
      className="max-h-[90vh] overflow-y-auto scrollbar-hide"
    >
      <div className="transition-all duration-300 ease-in-out">
        {mode === 'login' ? (
          <LoginForm
            key="login-form"
            onSwitchToSignup={() => handleSwitchMode('signup')}
            onAuthSuccess={handleAuthSuccess}
            isModal={true}
          />
        ) : (
          <SignupForm
            key="signup-form"
            onSwitchToLogin={() => handleSwitchMode('login')}
            onAuthSuccess={handleAuthSuccess}
            isModal={true}
          />
        )}
      </div>
    </Modal>
  );
};

export default AuthModal;
