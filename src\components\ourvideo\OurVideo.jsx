"use client";
import React, { useState } from 'react';
import Image from 'next/image';
import { Play } from 'lucide-react';
import SectionBadge from '../ui/SectionBadge';
import ActionButton from '../ui/ActionButton';

const OurVideo = () => {
    const [isVideoOpen, setIsVideoOpen] = useState(false);

    const handlePlayVideo = () => {
        setIsVideoOpen(true);
        // You can add video modal logic here
        console.log('Play video');
    };

    const handleLearnMore = () => {
        // Add learn more functionality here
        console.log('Learn more clicked');
    };

    return (
        <section className="sky-blue-bg py-8 sm:py-12 lg:py-16 xl:py-20">
            <div className="container mx-auto pl-4 sm:pl-6 lg:pl-0 xl:pl-12">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-4 xl:gap-12 items-end">
                    
                    {/* Left Side - Content */}
                    <div className="order-1 lg:order-1 lg:ml-30">
                        {/* Section Badge */}
                        <SectionBadge text="OUR VIDEO" isMobile={false} />
                        
                        {/* Main Heading */}
                        <h2 className="text-xl sm:text-2xl lg:text-4xl xl:text-4xl font-bold text-[#0C2C7A] dark:text-white my-4 leading-tight">
                            Let's Check Our <span className="text-[#24BDC7]">Latest Update</span> And Video
                        </h2>
                        
                        {/* Description */}
                        <p className="text-[#757F95] dark:text-gray-300 text-xs sm:text-sm leading-relaxed mb-6 max-w-2xl">
                            There are many variations of passages available but the majority have suffered alteration in some form injected humour if you are going to use passage you need sure there isn't anything look even slightly believable.
                        </p>
                        
                        {/* Action Button */}
                        <ActionButton 
                            text="Learn More" 
                            isMobile={false} 
                            onClick={handleLearnMore}
                        />
                    </div>

                    {/* Right Side - Video/Image */}
                    <div className="order-2 lg:order-2">
                        <div className="relative group cursor-pointer" onClick={handlePlayVideo}>
                        {/* Video Thumbnail/Background Image */}
                                                    <div className="relative w-full h-[200px] sm:h-[250px] lg:h-[300px] xl:h-[350px] rounded-l-full sm:rounded-l-full lg:rounded-l-full overflow-hidden shadow-2xl">
                                                        <Image
                                                            src="/assets/img/video/01.jpg"
                                                            alt="Video Thumbnail"
                                                            fill
                                                            className="object-cover group-hover:scale-105 transition-transform duration-500"
                                                            sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 45vw"
                                                        />
                                                        
                                                        {/* Overlay */}
                                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-all duration-300"></div>
                                
                                {/* Play Button */}
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center shadow-2xl transform group-hover:scale-110 transition-all duration-300 animate-pulse">
                                        <Play 
                                            className="text-white ml-1" 
                                            size={24}
                                            fill="currentColor"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Video Modal (Optional) */}
            {isVideoOpen && (
                <div 
                    className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
                    onClick={() => setIsVideoOpen(false)}
                >
                    <div className="relative w-full max-w-4xl aspect-video">
                        <iframe
                            src="https://www.youtube.com/embed/gxYF7ljdKgk?si=12345abcdefg"
                            title="Our Video"
                            className="w-full h-full rounded-lg"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                        ></iframe>
                        <button
                            onClick={() => setIsVideoOpen(false)}
                            className="absolute -top-10 right-0 text-white hover:text-gray-300 text-2xl"
                        >
                            ✕
                        </button>
                    </div>
                </div>
            )}
        </section>
    );
};

export default OurVideo;