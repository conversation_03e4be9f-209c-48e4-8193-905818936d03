"use client";
import React from "react";
import Image from "next/image";
import { ArrowRight } from "lucide-react";

const OfferCard = ({ image, discount, title, description, onLearnMore }) => {
  return (
    <div className="bg-white rounded-2xl sm:rounded-3xl overflow-hidden flex flex-col lg:flex-row h-full">
      {/* Image Section */}
      <div className="relative w-full h-[130px] sm:h-[140px] md:h-[160px] lg:w-1/2 lg:h-[185px] xl:h-[205px]">
        <Image
          src={image}
          alt={title}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 100vw"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
      </div>

      {/* Content Section */}
      <div className="p-6 lg:py-2 lg:px-4 xl:p-4 lg:w-1/2 flex flex-col gap-2 md:gap4 lg:gap-0 justify-between">
        {/* Discount Badge */}
        <div className="mb-1 sm:mb-2 ">
          <span className="text-xs sm:text-sm md:text-base lg:text-[16px] xl:text-[18px]   font-bold text-[#0C2C7A]">
            Get Upto <span className="text-[#24BDC7]">{discount}%</span>
          </span>
          <div className="text-xs sm:text-sm md:text-base lg:text-[12px]  font-semibold text-[#0C2C7A]">
            {title}
          </div>
        </div>

        {/* Description */}
        <p className="text-xs sm:text-sm md:text-base lg:text-[11px] text-[#757F95] leading-relaxed flex-grow">
          {description}
        </p>

        {/* Learn More Button */}
        <button 
          onClick={onLearnMore}
          className="flex items-center gap-3 justify-center bg-[#24BDC7] hover:bg-[#1ea8b2] text-xs sm:text-sm lg:text-[10px] xl:text-[10px] font-bold text-white rounded-full py-1 sm:py-2 lg:py-1 xl:py-2 px-3 sm:px-4 lg:px-[1px] transition-all duration-300 transform hover:scale-105 w-1/2 sm:w-auto lg:w-[77%] xl:w-[65%]"
        >
          Learn More
          <ArrowRight
            className="bg-white rounded-full p-0.5 text-[#24BDC7] flex-shrink-0"
            size={12}
          />
        </button>
      </div>
    </div>
  );
};

export default OfferCard;
