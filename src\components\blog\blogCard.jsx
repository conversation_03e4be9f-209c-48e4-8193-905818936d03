import Image from "next/image";
import ActionButton from "../ui/ActionButton";

export default function BlogCard({ blog }) {
  return (
    <div className="bg-white shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden rounded-tl-[20%] rounded-tr-[18%] rounded-br-[20%] my-4 sm:my-6 md:my-8 p-4 sm:p-5 w-full max-w-sm mx-auto">
      <div className="relative h-48 sm:h-56 md:h-60">
        <div className="absolute top-[-8%] sm:top-[-10%] left-[12%] sm:left-[16%] bg-white z-10 rounded-full shadow-md">
          <p className="text-xs sm:text-sm text-gray-500 p-2 sm:p-3">{blog.date}</p>
        </div>
        <Image
          src={blog.image}
          alt={blog.title}
          fill
          className="rounded-tl-[25%] rounded-tr-[25%] rounded-br-[26%] object-cover z-0"
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
        />
      </div>
      <div className="p-3 sm:p-4">
        <div className="flex items-center gap-2 text-gray-600 text-xs sm:text-sm mt-1 justify-between">
          <span className="truncate">{blog.author}</span>
          <span className="whitespace-nowrap">{blog.comments} Comments</span>
        </div>
        <h3 className="font-semibold text-base sm:text-lg mt-2 mb-3 sm:mb-4 line-clamp-2 leading-tight">{blog.title}</h3>
         <ActionButton text="Learn More" isMobile={true} />
      </div>
    </div>
  );
}
