"use client";
import React from 'react';
import FAQ from './FAQ';
import ContactCard from './ContactCard';
import OrganizedBy from './OrganizedBy';
import BookingWidget from './BookingWidget';
import WhyBookWithUs from './WhyBookWithUs';

const SidebarWidgets = ({ 
  showFAQ = true,
  showContact = true,
  showOrganizer = true,
  showBooking = true,
  showWhyBook = true,
  faqData = [],
  contactData = {},
  organizerData = {},
  bookingData = {},
  whyBookData = {},
  className = ""
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Booking Widget */}
      {showBooking && (
        <BookingWidget 
          {...bookingData}
          onBookNow={() => console.log('Book Now clicked')}
          onAddToWishlist={(isWishlisted) => console.log('Wishlist:', isWishlisted)}
        />
      )}

      {/* FAQ Component */}
      {showFAQ && (
        <FAQ 
          faqs={faqData}
          {...(faqData.title && { title: faqData.title })}
        />
      )}

      {/* Contact Card */}
      {showContact && (
        <ContactCard {...contactData} />
      )}

      {/* Organized By */}
      {showOrganizer && (
        <OrganizedBy 
          {...organizerData}
          onSendMessage={() => console.log('Send message clicked')}
        />
      )}

      {/* Why Book With Us */}
      {showWhyBook && (
        <WhyBookWithUs {...whyBookData} />
      )}
    </div>
  );
};

export default SidebarWidgets;
