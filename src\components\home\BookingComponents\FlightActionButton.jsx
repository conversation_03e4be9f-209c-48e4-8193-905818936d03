// components/home/<USER>/FlightActionButton.jsx
import React from 'react';

const FlightActionButton = ({
    action, // 'add' or 'remove'
    onAction,
    segmentId,
    type = 'flight', // 'flight' or 'hotel'
    className = ""
}) => {
    const isAddAction = action === 'add';
    const itemType = type === 'hotel' ? 'Hotel' : 'Flight';
    const itemTypePlural = type === 'hotel' ? 'Hotels' : 'Flights';

    const handleClick = () => {
        if (isAddAction) {
            onAction(); // For add action, no segmentId needed
        } else {
            onAction(segmentId); // For remove action, pass segmentId
        }
    };

    // Dynamic height based on type
    const buttonHeight = type === 'hotel' ? 'min-h-[4.3rem]' : 'min-h-[4.3rem]';

    return (
        <div className={`col-span-12 lg:col-span-3 text-[#0C2C7A] ${className}`}>
            <div className={`relative bg-[#E4F7F8] rounded-lg p-1 border-2 border-[#E4F7F8] transition-colors ${buttonHeight} flex flex-col justify-between`}>
                

                {/* Button Content */}
                <div className="flex-1 flex items-center justify-center">
                    <button
                        onClick={handleClick}
                        className={`w-full h-full flex items-center justify-center gap-2 px-4 py-3 rounded-lg transition-colors font-medium text-sm ${
                            isAddAction
                                ? 'bg-[#E4F7F8] text-[#24BDC7] '
                                : ' text-red-600'
                        }`}
                    >
                        <div className={`w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 ${
                            isAddAction ? 'bg-[#24BDC7]' : 'bg-red-600'
                        }`}>
                            <svg
                                className="w-3 h-3 text-white"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                {isAddAction ? (
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M12 4v16m8-8H4"
                                    />
                                ) : (
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                    />
                                )}
                            </svg>
                        </div>
                        {isAddAction ? `Add ${itemType}` : 'Remove'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default FlightActionButton;
