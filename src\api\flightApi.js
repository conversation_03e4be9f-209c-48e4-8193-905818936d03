import axiosInstance from "./axiosInstance";
import { flightSearchHandler } from "./handlers/flightHandler";

/**
 * Frontend API Layer
 * This layer uses handlers to communicate with backend APIs
 * Handlers handle request/response transformations
 */

// Flight Search API - uses handler for flexibility
export const searchFlights = async (payload) => {
  // Use the handler instead of direct API call
  // If backend API changes, only modify the handler
  return await flightSearchHandler.searchFlights(payload);
};

// Legacy direct API call (commented out - use handler instead)
/*
export const searchFlights = async (payload) => {
  const response = await axiosInstance.post("/api/v1/flight", payload);
  return response.data;
};
*/
