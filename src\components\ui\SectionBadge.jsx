import React from 'react';
import { Plane } from 'lucide-react';

const SectionBadge = ({ text, isMobile = false }) => {
  return (
    <div
      className={`inline-flex items-center bg-[#24BDC7] text-white w-fit 
      px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 font-semibold rounded-full shadow-md`}
    >
      {/* Icon Circle */}
      <div className="bg-white rounded-full w-5 h-5 sm:w-5 sm:h-5 flex items-center justify-center mr-2 sm:mr-3 relative">
        <Plane
          className="text-[#24BDC7] rotate-45"
          size={isMobile ? 12 : 14}
        />
      </div>

      {/* Text */}
      <span className="text-xs sm:text-sm md:text-base tracking-wide">
        {text}
      </span>
    </div>
  );
};

export default SectionBadge;
