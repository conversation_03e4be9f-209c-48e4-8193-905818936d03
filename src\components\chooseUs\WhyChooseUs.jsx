import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlane } from '@fortawesome/free-solid-svg-icons';
import FeatureCard from './FeatureCard';
import SectionBadge from '../ui/SectionBadge';

const WhyChooseUs = () => {
  // Feature data array
  const featuresData = [
    {
      id: 1,
      icon: "safety.svg",
      title: "Safety And Trust",
      description: "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout.",
      offset: 0
    },
    {
      id: 2,
      icon: "price.svg",
      title: "100% Price Transparency",
      description: "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout.",
      offset: 4
    },
    {
      id: 3,
      icon: "booking-confirm.svg",
      title: "Travel With More Confidence",
      description: "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout.",
      offset: 8
    }
  ];

return (
    <section className="w-full bg-white py-6 sm:py-8 md:py-10 lg:py-12 xl:py-16">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 xl:px-12">
            {/* Badge */}
            <div className="flex justify-center mb-4 sm:mb-6 md:mb-8 lg:mb-10">
                <SectionBadge text="CHOOSE US" isMobile={false} />
            </div>

            {/* Main Heading */}
            <div className="text-center mb-6 sm:mb-8 md:mb-10 lg:mb-12 xl:mb-14">
                <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-[#0C2C7A] leading-tight px-2 sm:px-4">
                    Discover Beautiful Place With Us
                </h2>
            </div>

            {/* Main Content */}
            <div className="flex flex-col items-center lg:flex-row gap-6 sm:gap-8 md:gap-10 lg:gap-1 xl:gap-4">
                {/* Left Side - Features */}
                <div className="w-full lg:w-1/2 space-y-3 sm:space-y-4 md:space-y-5 p-4 md:p-8">
                    {featuresData.map((feature) => (
                        <FeatureCard
                            key={feature.id}
                            number={feature.number}
                            icon={feature.icon}
                            title={feature.title}
                            description={feature.description}
                            offset={feature.offset}
                        />
                    ))}
                </div>

                {/* Right Side - Image Composition */}
                <div className="w-full lg:w-1/2 relative">
                    <div className="relative h-80 sm:h-96 md:h-[420px] lg:h-[480px] xl:h-[520px] 2xl:h-[560px] flex items-center justify-center">
                        {/* Shape Image Above Images */}
                        <img
                            src="/assets/img/shape/04.png"
                            alt="Shape"
                            className="absolute -mt-8 md:-mt-16 top-0 sm:top-1 md:top-0 lg:top-22 left-[30%] sm:left-[32%] md:left-[35%] transform -translate-x-1/2 w-35 h-35 sm:w-46 sm:h-46 z-0 md:w-56 md:h-56 lg:w-40 lg:h-40 xl:w-44 xl:h-44 2xl:w-48 2xl:h-48 object-contain  opacity-80"
                        />

                        {/* Two Circular Images Side by Side */}
                        <div className="relative w-full h-full flex items-center justify-center gap-2 sm:gap-3 md:gap-4 lg:gap-5">
                            <img
                                src="/assets/img/choose/02.jpg"
                                alt="Beautiful destination 1"
                                className="w-2/5 sm:w-[35%] md:w-[32%] lg:w-[42%] xl:w-[38%] aspect-[3/4] rounded-full object-cover mt-6 sm:mt-8 md:mt-10 lg:mt-8 xl:mt-14 2xl:mt-16 z-1 border-4 sm:border-6 md:border-8 border-white"
                            />
                            <img
                                src="/assets/img/choose/01.jpg"
                                alt="Beautiful destination 2"
                                className="w-1/2 sm:w-[45%] md:w-[42%] lg:w-[55%] xl:w-[48%] 2xl:w-[36%] aspect-[4/5] rounded-full object-cover -ml-6 sm:-ml-8 md:-ml-10 lg:-ml-12 xl:-ml-14 2xl:-ml-16 "
                            />
                        </div>

                        </div>
                </div>
            </div>
        </div>
    </section>
);
};

export default WhyChooseUs;
