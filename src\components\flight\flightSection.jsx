import FlightCard from './flightCard';
import flightsData from '../../app/data/flightData.json';
import SectionBadge from '../ui/SectionBadge';
import { Plane } from 'lucide-react';
import ActionButton from '../ui/ActionButton';
import { useState, useEffect } from 'react';

export default function FlightSection() {
  const flights = flightsData.slice(0, 8); // Limit to 8 cards
  const [itemsPerView, setItemsPerView] = useState(4); // Default to 4 for desktop

  useEffect(() => {
    const updateItemsPerView = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth < 640) {
          setItemsPerView(1); // Small screens: 1 card
        } else if (window.innerWidth < 1024) {
          setItemsPerView(3); // Tablet: 2 cards
        } else {
          setItemsPerView(4); // Desktop: 4 cards
        }
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  return (
    <section className="container max-w-6xl mx-auto bg-white py-5 ">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 md:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#0C2C7A]">
            Our Most Popular Flights
          </h2>
        </div>

        {/* Flight Cards Grid */}
        <div
          className="grid gap-2 sm:gap-4"
          style={{ gridTemplateColumns: `repeat(${itemsPerView}, minmax(0, 1fr))` }}
        >
          {flights.map((flight) => (
            <FlightCard key={flight.id} {...flight} />
          ))}
        </div>
      </div>
    </section>
  );
}