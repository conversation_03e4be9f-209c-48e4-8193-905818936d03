name: SSH to Remote Server

on:
  push:
    branches:
      - main
  workflow_dispatch:  # Change this to the branch you want to trigger on

jobs:
  ssh:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up SSH
      run: |
        sudo apt-get update
        sudo apt-get install -y sshpass
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh

    - name: Add SSH Host Key
      run: |
        ssh-keyscan -H ************* >> ~/.ssh/known_hosts || { echo "Failed to add host key"; exit 1; }

    - name: Run Remote Script
      env:
        SSH_PASSWORD: ${{ secrets.SSH_PASSWORD }}
      run: |
        sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no dev@************* "bash /home/<USER>/travel-tours-frontend/deploy.sh" || { echo "SSH command failed"; exit 1; }
