"use client";
import React, { useState } from 'react';
import { MapPin, ChevronDown, BaggageClaim } from 'lucide-react';

const BaggageSection = ({ 
  title = "Baggage",
  className = ""
}) => {
  const [fromLocation, setFromLocation] = useState('');
  const [toLocation, setToLocation] = useState('');
  const [tierStatus, setTierStatus] = useState('');
  const [bagWeight, setBagWeight] = useState('');
  const [flightClass, setFlightClass] = useState('');

  const baggageInfo = [
    { icon: BaggageClaim, label: "Carry-on Allowance", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Damaged Baggage", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Baggage Status", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Baggage Tips", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Lost and Found", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Baggage Allowance", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Restricted Items", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Delayed Baggage", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Baggage Services", color: "bg-[#24BDC7]" },
    { icon: BaggageClaim, label: "Liability Limitations", color: "bg-[#24BDC7]" }
  ];

  const description = "There are many variations of passengers of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable.";

  return (
    <div className={`p-6 ${className}`}>
      <h3 className="text-xl font-bold text-[#0C2C7A] mb-6">{title}</h3>
      
      {/* Location and Details Form */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* From Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">From</label>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="City, Airport, Country Name"
              value={fromLocation}
              onChange={(e) => setFromLocation(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
            />
          </div>
        </div>

        {/* To Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">To</label>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="City, Airport, Country Name"
              value={toLocation}
              onChange={(e) => setToLocation(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
            />
          </div>
        </div>

        {/* Top Tier Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Top Tier Status</label>
          <div className="relative">
            <select
              value={tierStatus}
              onChange={(e) => setTierStatus(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent appearance-none"
            >
              <option value="">Select Tier Status</option>
              <option value="gold">Gold</option>
              <option value="silver">Silver</option>
              <option value="bronze">Bronze</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          </div>
        </div>

        {/* Bag Weight */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Bag Weight (Kgs)</label>
          <div className="relative">
            <input
              type="text"
              placeholder="Bag Weight"
              value={bagWeight}
              onChange={(e) => setBagWeight(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent"
            />
          </div>
        </div>

        {/* Flight Class */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Flight Class</label>
          <div className="relative">
            <select
              value={flightClass}
              onChange={(e) => setFlightClass(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#24BDC7] focus:border-transparent appearance-none"
            >
              <option value="">Select Class</option>
              <option value="economy">Economy</option>
              <option value="business">Business</option>
              <option value="first">First Class</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          </div>
        </div>
      </div>

      {/* Check Baggage Allowance Button */}
      <button className="w-fit bg-[#24BDC7]  text-white py-3 px-6 rounded-lg font-semibold hover:bg-[#1ea5ae] transition-colors mb-6">
        Check Baggage Allowance
      </button>

      {/* Description */}
      <p className="text-gray-600 text-sm leading-relaxed mb-6">
        {description}
      </p>

      {/* Baggage Info Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {baggageInfo.map((item, index) => (
          <div key={index} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className={`w-8 h-8 ${item.color} rounded-full rounded-bl-none flex items-center justify-center flex-shrink-0`}>
              <item.icon size={16} className="text-white" />
            </div>
            <span className="text-sm font-medium text-[#0C2C7A]">
              {item.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BaggageSection;