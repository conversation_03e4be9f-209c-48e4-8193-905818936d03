"use client";
import React from 'react';
import { Wifi, Coffee, Gamepad2, Tv, Utensils, Armchair, WineIcon, Image, Music, AirVent } from 'lucide-react';

const InflightFeatures = ({
  title = "Inflight Features",
  features = [],
  className = ""
}) => {
  // Default features with proper icons
  const defaultFeatures = [
    { icon: Wifi, label: "WiFi", color: "bg-[#24BDC7]" },
    { icon: Tv, label: "Television", color: "bg-[#24BDC7]" },
    { icon: Music, label: "Entertainment", color: "bg-[#24BDC7]" },
    { icon: Coffee, label: "Coffee", color: "bg-[#24BDC7]" },
    { icon: AirVent, label: "Air Conditioning", color: "bg-[#24BDC7]" },
    { icon: Utensils, label: "Food", color: "bg-[#24BDC7]" },
    { icon: Gamepad2, label: "Games", color: "bg-[#24BDC7]" },
    { icon: WineIcon, label: "Wines", color: "bg-[#24BDC7]" },
    { icon: Armchair, label: "Comfort", color: "bg-[#24BDC7]" },
    { icon: Image, label: "Magazines", color: "bg-[#24BDC7]" }
  ];

  const featureList = features.length > 0 ? features : defaultFeatures;

  return (
    <div className={`p-2 ${className}`}>
      <h3 className="text-xl font-bold text-[#0C2C7A] mb-6">{title}</h3>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {featureList.map((feature, index) => {
          const IconComponent = feature.icon;

          return (
            <div key={index} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <div className={`w-8 h-8 ${feature.color} rounded-full rounded-bl-none flex items-center justify-center flex-shrink-0`}>
                <IconComponent size={16} className="text-white" />
              </div>
              <span className="text-sm font-medium text-[#0C2C7A]">
                {feature.label}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default InflightFeatures;
