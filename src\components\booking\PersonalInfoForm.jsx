"use client";
import React from 'react';
import { User, Mail, Phone, MapPin, Globe, MessageSquare } from 'lucide-react';
import FormField from '../ui/FormField';
import FormSection from '../ui/FormSection';

const PersonalInfoForm = ({ formData, onChange }) => {
  const countryOptions = [
    { value: 'US', label: 'United States' },
    { value: 'UK', label: 'United Kingdom' },
    { value: 'CA', label: 'Canada' },
    { value: 'AU', label: 'Australia' },
    { value: 'PK', label: 'Pakistan' },
    { value: 'IN', label: 'India' }
  ];

  return (
    <FormSection 
      title="Booking Personal Info" 
      // icon={User}
      subtitle="Please provide your personal information for the booking"
    >
      <div className="space-y-4">
        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="First Name"
            name="firstName"
            value={formData.firstName}
            onChange={onChange}
            placeholder="First Name"
            icon={User}
            required
          />
          <FormField
            label="Last Name"
            name="lastName"
            value={formData.lastName}
            onChange={onChange}
            placeholder="Last Name"
            icon={User}
            required
          />
        </div>

        {/* Contact Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Email"
            name="email"
            type="email"
            value={formData.email}
            onChange={onChange}
            placeholder="Email Address"
            icon={Mail}
            required
          />
          <FormField
            label="Phone"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={onChange}
            placeholder="Phone Number"
            icon={Phone}
            required
          />
        </div>

        {/* Address Fields */}
        <FormField
          label="Address 1"
          name="address1"
          value={formData.address1}
          onChange={onChange}
          placeholder="Address Line"
          icon={MapPin}
          required
        />

        <FormField
          label="Address 2"
          name="address2"
          value={formData.address2}
          onChange={onChange}
          placeholder="Address Line"
          icon={MapPin}
        />

        {/* Country and Age */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Country"
            name="country"
            type="select"
            value={formData.country}
            onChange={onChange}
            options={countryOptions}
            icon={Globe}
            required
          />
          <FormField
            label="Age"
            name="age"
            type="number"
            value={formData.age}
            onChange={onChange}
            placeholder="Age"
            icon={User}
            required
          />
        </div>

        {/* City, State, Zip */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            label="City"
            name="city"
            value={formData.city}
            onChange={onChange}
            placeholder="City"
            icon={MapPin}
            required
          />
          <FormField
            label="State"
            name="state"
            value={formData.state}
            onChange={onChange}
            placeholder="State"
            icon={MapPin}
            required
          />
          <FormField
            label="Zip Code"
            name="zipCode"
            value={formData.zipCode}
            onChange={onChange}
            placeholder="Zip Code"
            icon={MapPin}
            required
          />
        </div>

        {/* Additional Info */}
        <FormField
          label="Additional Info"
          name="additionalInfo"
          type="textarea"
          value={formData.additionalInfo}
          onChange={onChange}
          placeholder="Additional Comment"
          icon={MessageSquare}
          rows={4}
        />
      </div>
    </FormSection>
  );
};

export default PersonalInfoForm;
