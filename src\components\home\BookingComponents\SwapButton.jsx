// components/home/<USER>/SwapButton.jsx
import React from 'react';

const SwapButton = ({ onSwap }) => {
  return (
    <button 
      onClick={onSwap}
      className="w-5 h-5 bg-[#24BDC7] hover:bg-[#1da5ae] text-white rounded-full flex items-center justify-center border-1 border-white shadow-md transition-all duration-200 hover:scale-105 z-10 cursor-pointer"
      type="button"
    >
      <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
      </svg>
    </button>
  );
};

export default SwapButton;
