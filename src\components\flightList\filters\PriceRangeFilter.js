import React, { useState } from 'react';
import FilterSection from './FilterSection';

const PriceRangeFilter = ({ filters, onFilterChange }) => {
  const [priceRange, setPriceRange] = useState({
    min: filters.priceMin || 0,
    max: filters.priceMax || 1000
  });

  const handlePriceChange = (type, value) => {
    const newRange = { ...priceRange, [type]: parseInt(value) };
    setPriceRange(newRange);
    onFilterChange('priceRange', newRange);
  };

  const sliderClasses = `
    w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-[#24BDC7]
    [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5 
    [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-[#24BDC7] [&::-webkit-slider-thumb]:cursor-pointer
    [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-white [&::-webkit-slider-thumb]:shadow-md
    [&::-webkit-slider-thumb]:hover:scale-110 [&::-webkit-slider-thumb]:transition-transform
    [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:rounded-full 
    [&::-moz-range-thumb]:bg-[#24BDC7] [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:border-2 
    [&::-moz-range-thumb]:border-white [&::-moz-range-thumb]:shadow-md [&::-moz-range-thumb]:border-none
  `;

  return (
    <FilterSection title="Flight Price">
      <div className="px-2">
        <div className="flex items-center justify-between text-sm mb-2">
          <span>Price: ${priceRange.min} - ${priceRange.max}</span>
        </div>
        <div className="space-y-3">
          <div>
            <label className="block text-xs mb-1">Minimum</label>
            <input
              type="range"
              min="0"
              max="1000"
              value={priceRange.min}
              onChange={(e) => handlePriceChange('min', e.target.value)}
              className={sliderClasses}
            />
          </div>
          <div>
            <label className="block text-xs mb-1">Maximum</label>
            <input
              type="range"
              min="0"
              max="1000"
              value={priceRange.max}
              onChange={(e) => handlePriceChange('max', e.target.value)}
              className={sliderClasses}
            />
          </div>
        </div>
      </div>
    </FilterSection>
  );
};

export default PriceRangeFilter;
