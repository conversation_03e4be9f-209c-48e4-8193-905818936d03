// src/api/handlers/flightHandler.js
import axiosInstance from "../axiosInstance";

/**
 * Flight Search Handler
 * This handler sits between the frontend and the backend API
 * Handles both request transformation and response processing
 * If the backend API changes, only modify this file
 */
export const flightSearchHandler = {
  /**
   * Transform frontend payload to backend API format
   * @param {Object} payload - Frontend payload
   * @returns {Object} - Transformed payload for backend
   */
  transformRequest: (payload) => {
    // Current backend expects: { flightType: "oneWay", legs: [...] }
    // If backend changes, modify this transformation

    const transformedPayload = {
      flightType: payload.flightType,
      legs: payload.legs.map(leg => ({
        from: leg.from,
        to: leg.to,
        date: leg.date
      }))
    };

    // Add any additional fields that the backend might need
    // transformedPayload.apiKey = process.env.FLIGHT_API_KEY;
    // transformedPayload.version = "v1";

    console.log("Transformed request payload:", transformedPayload);
    return transformedPayload;
  },

  /**
   * Transform backend response to frontend format
   * @param {Object} response - Backend API response
   * @returns {Object} - Transformed response for frontend
   */
  transformResponse: (response) => {
    // Current backend response structure
    // If backend changes response format, modify this transformation

    // Handle different possible response structures
    let flights = [];
    let totalResults = 0;
    let searchId = null;

    if (response) {
      // Handle various response formats
      if (Array.isArray(response)) {
        // Response is direct array of flights
        flights = response;
        totalResults = response.length;
      } else if (response.flights) {
        // Response has flights property
        flights = Array.isArray(response.flights) ? response.flights : [];
        totalResults = response.totalResults || response.total_count || flights.length;
        searchId = response.searchId || response.search_id || null;
      } else if (response.data) {
        // Response wrapped in data property
        const data = response.data;
        if (Array.isArray(data)) {
          flights = data;
          totalResults = data.length;
        } else if (data.flights) {
          flights = Array.isArray(data.flights) ? data.flights : [];
          totalResults = data.totalResults || data.total_count || flights.length;
          searchId = data.searchId || data.search_id || null;
        }
      }

      // Normalize flight objects
      flights = flights.map(flight => ({
        id: flight.id || flight.flightId || flight.flight_id || `flight_${Math.random()}`,
        airline: flight.airline || flight.airlineName || 'Unknown Airline',
        flightNumber: flight.flightNumber || flight.flight_number || flight.number || '',
        from: flight.from || flight.origin || flight.departure,
        to: flight.to || flight.destination || flight.arrival,
        departureTime: flight.departureTime || flight.departure_time || flight.departureDate,
        arrivalTime: flight.arrivalTime || flight.arrival_time || flight.arrivalDate,
        duration: flight.duration || flight.flightDuration,
        stops: flight.stops || flight.stopCount || 0,
        price: flight.price || flight.totalPrice || flight.amount || 0,
        currency: flight.currency || 'USD',
        class: flight.class || flight.cabinClass || 'economy',
        // Add any other fields that might be useful
        ...flight
      }));
    }

    const transformedResponse = {
      flights: flights,
      totalResults: totalResults,
      searchId: searchId,
      // Add any additional fields from backend
      ...response
    };

    // Remove any sensitive data that shouldn't go to frontend
    // delete transformedResponse.internalApiKey;

    console.log("Transformed response:", transformedResponse);
    return transformedResponse;
  },

  /**
   * Handle flight search API call
   * @param {Object} payload - Frontend payload
   * @returns {Promise<Object>} - Transformed response
   */
  searchFlights: async (payload) => {
    try {
      // Transform the request payload
      const transformedPayload = flightSearchHandler.transformRequest(payload);

      // Make the API call
      const response = await axiosInstance.post("/api/v1/flight", transformedPayload);

      // Transform the response
      const transformedResponse = flightSearchHandler.transformResponse(response.data);

      return transformedResponse;
    } catch (error) {
      console.error("Flight search handler error:", error);

      // Transform error response if needed - ensure serializable strings
      if (error.response) {
        // Backend returned an error
        const errorMessage = typeof error.response.data?.message === 'string'
          ? error.response.data.message
          : "Flight search failed";
        throw new Error(errorMessage);
      } else if (error.request) {
        // Network error
        throw new Error("Network error - please check your connection");
      } else {
        // Other error
        const errorMessage = typeof error.message === 'string'
          ? error.message
          : "An unexpected error occurred";
        throw new Error(errorMessage);
      }
    }
  }
};

export default flightSearchHandler;