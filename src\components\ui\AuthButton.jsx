import React from 'react';

const AuthButton = ({ 
  children,
  type = "button",
  onClick,
  disabled = false,
  variant = "primary",
  className = "",
  loading = false,
  ...props 
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-[#24BDC7] hover:bg-[#1ea8b2] text-white border-[#24BDC7]';
      case 'secondary':
        return 'bg-white hover:bg-gray-50 text-[#24BDC7] border-[#24BDC7]';
      case 'outline':
        return 'bg-transparent hover:bg-[#24BDC7] hover:text-white text-[#24BDC7] border-[#24BDC7]';
      default:
        return 'bg-[#24BDC7] hover:bg-[#1ea8b2] text-white border-[#24BDC7]';
    }
  };

  const baseClasses = "w-full py-2 px-6 border-2 rounded-xl font-semibold transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none";
  const variantClasses = getVariantClasses();

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={`${baseClasses} ${variantClasses} ${className}`}
      {...props}
    >
      {loading ? (
        <div className="flex items-center justify-center gap-2">
          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
          Loading...
        </div>
      ) : (
        children
      )}
    </button>
  );
};

export default AuthButton;
