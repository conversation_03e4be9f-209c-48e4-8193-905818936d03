import React from 'react';
import FlightClassFilter from './filters/FlightClassFilter';
import PriceRangeFilter from './filters/PriceRangeFilter';
import FlightTimeFilter from './filters/FlightTimeFilter';
import FlightStopsFilter from './filters/FlightStopsFilter';
import AirlinesFilter from './filters/AirlinesFilter';
import WeightsFilter from './filters/WeightsFilter';
import RefundableFilter from './filters/RefundableFilter';

const FlightFiltersNew = ({ onFilterChange, filters = {} }) => {
  const handleFilterChange = (filterType, value, checked = null) => {
    if (onFilterChange) {
      onFilterChange(filterType, value, checked);
    }
  };

  return (
    <div className="w-full lg:w-64 bg-white rounded-4xl p-6">
      <div className="space-y-0">
        <FlightClassFilter 
          filters={filters}
          onFilterChange={handleFilterChange}
        />
        
        <PriceRangeFilter 
          filters={filters}
          onFilterChange={handleFilterChange}
        />
        
        <FlightTimeFilter 
          filters={filters}
          onFilterChange={handleFilterChange}
        />
        
        <FlightStopsFilter 
          filters={filters}
          onFilterChange={handleFilterChange}
        />
        
        <AirlinesFilter 
          filters={filters}
          onFilterChange={handleFilterChange}
        />
        
        <WeightsFilter 
          filters={filters}
          onFilterChange={handleFilterChange}
        />
        
        <RefundableFilter 
          filters={filters}
          onFilterChange={handleFilterChange}
        />
      </div>
    </div>
  );
};

export default FlightFiltersNew;
