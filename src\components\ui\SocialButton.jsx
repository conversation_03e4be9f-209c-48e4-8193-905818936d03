import React from 'react';
import { FaFacebookF, FaGoogle } from 'react-icons/fa';

const SocialButton = ({ 
  provider, 
  onClick, 
  className = "",
  children,
  ...props 
}) => {
  const getProviderConfig = () => {
    switch (provider) {
      case 'facebook':
        return {
          icon: <FaFacebookF className="w-5 h-5" />,
          bgColor: 'bg-white',
          textColor: 'text-blue-500',
          borderColor: 'border-blue-500'
        };
      case 'google':
        return {
          icon: <FaGoogle className="w-5 h-5" />,
          bgColor: 'bg-white',
          textColor: 'text-red-500',
          borderColor: 'border-red-500'
        };
      default:
        return {
          icon: null,
          bgColor: 'bg-gray-100 hover:bg-gray-200',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-300'
        };
    }
  };

  const config = getProviderConfig();

  return (
    <button
      type="button"
      onClick={onClick}
      className={`w-full flex items-center justify-center gap-3 py-2 px-4 border-2 rounded-xl font-medium transition-all duration-200 transform hover:scale-[1.02] ${config.bgColor} ${config.textColor} ${config.borderColor} ${className}`}
      {...props}
    >
      {config.icon}
      {children}
    </button>
  );
};

export default SocialButton;
