"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import { Autoplay } from "swiper/modules";
import Image from "next/image";

const logos = [
    { id: 1, src: "/assets/img/partner/01.png" },
    { id: 2, src: "/assets/img/partner/02.png" },
    { id: 3, src: "/assets/img/partner/03.png" },
    { id: 4, src: "/assets/img/partner/04.png" },
    { id: 5, src: "/assets/img/partner/01.png" },
    { id: 6, src: "/assets/img/partner/02.png" },
    { id: 7, src: "/assets/img/partner/03.png" },
    { id: 8, src: "/assets/img/partner/04.png" },
];

export default function LogoSlider() {
    return (

        <div className="w-full pl-0 pr-4 py-6 bg-white z-20 shadow-lg rounded-r-full">
            <Swiper
                modules={[Autoplay]}
                autoplay={{ delay: 2000, disableOnInteraction: false }}
                loop={true}
                speed={1000}
                breakpoints={{
                    320: { slidesPerView: 2, spaceBetween: 10 },
                    640: { slidesPerView: 3, spaceBetween: 15 },
                    1024: { slidesPerView: 5, spaceBetween: 20 },
                    1280: { slidesPerView: 6, spaceBetween: 24 },
                }}
            >
                {logos.map((logo) => (
                    <SwiperSlide key={logo.id}>
                        <div className="bg-white flex items-center justify-center p-2 border border-gray-200 rounded-full  transition">
                            <Image
                                src={logo.src}
                                alt={`logo-${logo.id}`}
                                width={100}
                                height={60}
                                className="object-contain"
                            />
                        </div>
                    </SwiperSlide>
                ))}
            </Swiper>
        </div>

    );
}
