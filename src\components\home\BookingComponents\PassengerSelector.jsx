// components/home/<USER>/PassengerSelector.jsx
import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers } from '@fortawesome/free-solid-svg-icons';
import bookingConfigData from '../../../app/data/bookingConfigData.json';

const PassengerSelector = ({
  passengers = bookingConfigData.defaultPassengers,
  travelClass = bookingConfigData.defaultTravelClass,
  onPassengerChange,
  isMultiCity = false,
  segmentIndex = 0,
  onAddPassengerGroup,
  onRemovePassengerGroup
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [tempPassengers, setTempPassengers] = useState(passengers);
  const [tempTravelClass, setTempTravelClass] = useState(travelClass);
  const containerRef = useRef(null);

  // Travel class options from config
  const travelClasses = bookingConfigData.travelClasses.map(cls => ({
    value: cls,
    label: cls
  }));

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Update temp state when props change
  useEffect(() => {
    setTempPassengers(passengers);
    setTempTravelClass(travelClass);
  }, [passengers, travelClass]);

  const updatePassengerCount = (type, change) => {
    setTempPassengers(prev => {
      const newCount = Math.max(0, prev[type] + change);
      return { ...prev, [type]: newCount };
    });
  };

  const handleApply = () => {
    if (onPassengerChange) {
      onPassengerChange(tempPassengers, tempTravelClass);
    }
    setIsOpen(false);
  };

  const handleCancel = () => {
    setTempPassengers(passengers);
    setTempTravelClass(travelClass);
    setIsOpen(false);
  };

  const handleAddPassengerGroup = () => {
    if (onAddPassengerGroup) {
      onAddPassengerGroup();
    }
    setIsOpen(false);
  };

  const handleRemovePassengerGroup = () => {
    if (onRemovePassengerGroup) {
      onRemovePassengerGroup();
    }
    setIsOpen(false);
  };

  const totalPassengers = tempPassengers.adults + tempPassengers.children + tempPassengers.infants;
  const passengerText = totalPassengers === 1 ? '1 Passenger' : `${totalPassengers} Passengers`;

  return (
    <div className="col-span-12 sm:col-span-6 lg:col-span-3 text-[#0C2C7A]" ref={containerRef}>
      <div className={`relative bg-[#E4F7F8] rounded-xl p-1 border-2 transition-colors min-h-[4rem] flex flex-col justify-between ${
        isFocused ? 'border-[#24BDC7]' : 'border-[#E4F7F8]'
      }`}>
        <div className="flex items-center justify-between">
          <label className="block text-xs font-medium mb-0.5">
            {isMultiCity ? `${travelClass} - Flight ${segmentIndex + 1}` : `${travelClass} - Passenger`}
          </label>
          <FontAwesomeIcon icon={faUsers} className="text-[#24BDC7] text-sm" />
        </div>

        {/* Clickable Display */}
        <div
          className="flex-1 flex items-center justify-between cursor-pointer rounded-lg p-1 transition-colors "
          onClick={() => setIsOpen(!isOpen)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setTimeout(() => setIsFocused(false), 150)}
          tabIndex={0}
        >
          <div className="min-w-0 flex-1">
            <div className="text-sm lg:text-base font-semibold truncate">
              {passengerText}
              {isMultiCity && <span className="text-xs text-[#24BDC7] ml-2">Flight {segmentIndex + 1}</span>}
            </div>
          </div>
        </div>

        {/* Dropdown Panel */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-3 lg:p-4 max-h-80 lg:max-h-96 overflow-y-auto">
            {/* Travel Class Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Travel Class</label>
              <div className="grid grid-cols-2 gap-2">
                {travelClasses.map((cls) => (
                  <button
                    key={cls.value}
                    onClick={() => setTempTravelClass(cls.value)}
                    className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                      tempTravelClass === cls.value
                        ? 'bg-[#24BDC7] text-white border-[#24BDC7]'
                        : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {cls.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Passenger Selection */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700">Passengers</label>

              {/* Adults */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium">Adults</div>
                  <div className="text-xs text-gray-500">12+ years</div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => updatePassengerCount('adults', -1)}
                    disabled={tempPassengers.adults <= 1}
                    className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"/>
                    </svg>
                  </button>
                  <span className="w-8 text-center font-medium">{tempPassengers.adults}</span>
                  <button
                    onClick={() => updatePassengerCount('adults', 1)}
                    disabled={tempPassengers.adults >= 9}
                    className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"/>
                    </svg>
                  </button>
                </div>
              </div>

              {/* Children */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium">Children</div>
                  <div className="text-xs text-gray-500">2-11 years</div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => updatePassengerCount('children', -1)}
                    disabled={tempPassengers.children <= 0}
                    className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"/>
                    </svg>
                  </button>
                  <span className="w-8 text-center font-medium">{tempPassengers.children}</span>
                  <button
                    onClick={() => updatePassengerCount('children', 1)}
                    disabled={tempPassengers.children >= 8}
                    className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"/>
                    </svg>
                  </button>
                </div>
              </div>

              {/* Infants */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium">Infants</div>
                  <div className="text-xs text-gray-500">Under 2 years</div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => updatePassengerCount('infants', -1)}
                    disabled={tempPassengers.infants <= 0}
                    className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"/>
                    </svg>
                  </button>
                  <span className="w-8 text-center font-medium">{tempPassengers.infants}</span>
                  <button
                    onClick={() => updatePassengerCount('infants', 1)}
                    disabled={tempPassengers.infants >= tempPassengers.adults}
                    className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-4 pt-4 border-t border-gray-200 space-y-2">
              {/* Multi-City Buttons */}
              {isMultiCity && (
                <div className="flex gap-2">
                  <button
                    onClick={handleAddPassengerGroup}
                    className="flex-1 px-3 py-2 text-[#24BDC7] bg-[#E4F7F8] hover:bg-[#24BDC7] hover:text-white rounded-lg transition-colors text-sm font-medium"
                  >
                    + Add Group
                  </button>
                  {segmentIndex > 0 && (
                    <button
                      onClick={handleRemovePassengerGroup}
                      className="px-3 py-2 text-red-500 bg-red-50 hover:bg-red-500 hover:text-white rounded-lg transition-colors text-sm font-medium"
                    >
                      Remove
                    </button>
                  )}
                </div>
              )}

              {/* Standard Buttons */}
              <div className="flex gap-2">
                <button
                  onClick={handleCancel}
                  className="flex-1 px-4 py-2 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleApply}
                  className="flex-1 px-4 py-2 text-white bg-[#24BDC7] hover:bg-teal-600 rounded-lg transition-colors"
                >
                  Apply
                </button>
              </div>
            </div>
          </div>
        )}
        <div className="h-2"></div>
      </div>
    </div>
  );
};

export default PassengerSelector;
