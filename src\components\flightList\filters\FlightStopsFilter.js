import React from 'react';
import FilterSection from './FilterSection';
import CheckboxItem from './CheckboxItem';

const FlightStopsFilter = ({ filters, onFilterChange }) => {
  const stopOptions = [
    { label: 'Non Stop', count: '20' },
    { label: '1 Stop', count: '15' },
    { label: '2 Stop', count: '18' },
    { label: '3 Stop', count: '5' }
  ];

  return (
    <FilterSection title="Flight Stops">
      {stopOptions.map((stop) => (
        <CheckboxItem
          key={stop.label}
          label={stop.label}
          count={stop.count}
          checked={filters.stops?.includes(stop.label)}
          onChange={(e) => onFilterChange('stops', stop.label, e.target.checked)}
        />
      ))}
    </FilterSection>
  );
};

export default FlightStopsFilter;
