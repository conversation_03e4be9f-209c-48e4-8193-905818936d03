"use client";
import React, { useState } from 'react';
import { CreditCard, User, Calendar, Shield } from 'lucide-react';
import FormField from '../ui/FormField';
import FormSection from '../ui/FormSection';
import PaymentMethodCard from '../ui/PaymentMethodCard';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';

const PaymentForm = ({ formData, onChange, onSubmit, loading = false }) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('credit');

  const paymentMethods = [
    {
      id: 'credit',
      title: 'Payment With Credit Card',
      subtitle: 'Visa, Mastercard, American Express',
      icon: (
        <div className="flex space-x-1 justify-center">
          <div className="w-8 h-5 rounded overflow-hidden">
            <Image src="/assets/img/payment/mastercard.svg" alt="masterCard" width={32} height={20} className="object-contain" />
          </div>
          <div className="w-8 h-5 rounded overflow-hidden">
            <Image src="/assets/img/payment/visa.svg" alt="Visa" width={32} height={20} className="object-contain" />
          </div>
          <div className="w-8 h-5 rounded overflow-hidden">
            <Image src="/assets/img/payment/american-express.svg" alt="americanExpress" width={32} height={20} className="object-contain" />
          </div>
          <div className="w-8 h-5 rounded overflow-hidden">
            <Image src="/assets/img/payment/discover.svg" alt="discover" width={32} height={20} className="object-contain" />
          </div>
        </div>
      )
    },
    {
      id: 'paypal',
      title: 'Payment With PayPal',
      subtitle: 'Pay securely with PayPal',
      icon: <div className="flex justify-center">
        <Image src="/assets/img/payment/paypal-2.svg" alt="paypal" width={60} height={40} className="object-contain" />
      </div>
    },
    {
      id: 'payoneer',
      title: 'Payment With Payoneer',
      subtitle: 'Global payment solution',
      icon: <div className="flex justify-center">
        <Image src="/assets/img/payment/payoneer.svg" alt="payoneer" width={60} height={40} className="object-contain" />
      </div>
    }
  ];

  return (
    <FormSection 
      title="Your Card Information" 
      // icon={CreditCard}
      subtitle="Choose your preferred payment method and enter card details"
    >
      <div className="space-y-6">
        {/* Payment Method Selection */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-4">Select Payment Method</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {paymentMethods.map((method) => (
              <PaymentMethodCard
                key={method.id}
                method={method.id}
                isSelected={selectedPaymentMethod === method.id}
                onSelect={setSelectedPaymentMethod}
                icon={method.icon}
                title={method.title}
                // subtitle={method.subtitle}
              />
            ))}
          </div>
        </div>

        {/* Credit Card Form - Show only if credit card is selected */}
        {selectedPaymentMethod === 'credit' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Card Holder Name"
                name="cardHolderName"
                value={formData.cardHolderName}
                onChange={onChange}
                placeholder="Name On Card"
                icon={User}
                required
              />
              <FormField
                label="Card Number"
                name="cardNumber"
                value={formData.cardNumber}
                onChange={onChange}
                placeholder="Your Card Number"
                icon={CreditCard}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Expire Date"
                name="expiryDate"
                value={formData.expiryDate}
                onChange={onChange}
                placeholder="MM/YY"
                icon={Calendar}
                required
              />
              <FormField
                label="CCV"
                name="cvv"
                value={formData.cvv}
                onChange={onChange}
                placeholder="CCV"
                icon={Shield}
                required
              />
              <div className="flex items-center justify-start mt-4">
              <button className="flex gap-4 bg-[#24BDC7] text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-[#1da8b3] transition-colors">
                Confirm Booking
 <ArrowRight
        className="bg-white rounded-full p-1 text-[#24BDC7] items-center justify-center my-auto"
        size={16}
      />
              </button>
            </div>
            </div>
          </div>
        )}

        {/* PayPal Form */}
        {selectedPaymentMethod === 'paypal' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Email Address"
                name="paypalEmail"
                type="email"
                value={formData.paypalEmail || ''}
                onChange={onChange}
                placeholder="Email"
                icon={User}
                required
              />
              <FormField
                label="Password"
                name="paypalPassword"
                type="password"
                value={formData.paypalPassword || ''}
                onChange={onChange}
                placeholder="Password"
                icon={Shield}
                required
              />
            </div>
            <div className="flex items-center justify-start mt-6">
              <button className="flex gap-4 bg-[#24BDC7] text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-[#1da8b3] transition-colors">
                Login Account 
               <ArrowRight
        className="bg-white rounded-full p-1 text-[#24BDC7] items-center justify-center my-auto"
        size={16}
      />
              </button>
            </div>
          </div>
        )}

        {/* Payoneer Form */}
        {selectedPaymentMethod === 'payoneer' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Email Address"
                name="payoneerEmail"
                type="email"
                value={formData.payoneerEmail || ''}
                onChange={onChange}
                placeholder="Email"
                icon={User}
                required
              />
              <FormField
                label="Password"
                name="payoneerPassword"
                type="password"
                value={formData.payoneerPassword || ''}
                onChange={onChange}
                placeholder="Password"
                icon={Shield}
                required
              />
            </div>
            <div className="flex items-center justify-start mt-6">
              <button className="flex gap-4 bg-[#24BDC7] text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-[#1da8b3] transition-colors">
                Login Account
                <ArrowRight
        className="bg-white rounded-full p-1 text-[#24BDC7] items-center justify-center my-auto"
        size={16}
      />
              </button>
            </div>
          </div>
        )}

        {/* Submit Button */}
        {/* <div className="pt-4">
            <ActionButton text="Confirm Booking" onClick={onSubmit} isMobile={true} />
        </div> */}
      </div>
    </FormSection>
  );
};

export default PaymentForm;
