"use client";
import React, { useState } from 'react';
import { Calendar, Users, Heart, Share2, Eye, Calendar1Icon, PersonStandingIcon, User2 } from 'lucide-react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCalendarDays } from '@fortawesome/free-solid-svg-icons';
import { useRouter } from 'next/navigation';

const BookingWidget = ({
  price = "",
  originalPrice = "",
  journeyDate = "",
  journeyDay = "",
  returnDate = "",
  returnDay = "",
  passengers = "",
  passengerClass = "",
  views = "",
  shares = "",
  fareId = "fare-0001", // Add fareId prop
  flightCardData = {}, // Add flight card data prop
  onBookNow,
  onAddToWishlist
}) => {
  const [isWishlisted, setIsWishlisted] = useState(false);
  const router = useRouter();

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    if (onAddToWishlist) {
      onAddToWishlist(!isWishlisted);
    }
  };

  const handleBookNow = () => {
    // Store fareId and flight data in localStorage for booking page
    localStorage.setItem('selectedFareId', fareId);

    // Store flight booking data with flight card details
    const flightBookingData = {
      fareId,
      price,
      originalPrice,
      journeyDate,
      journeyDay,
      returnDate,
      returnDay,
      passengers,
      passengerClass,
      // Flight card data
      route: flightCardData.route || "New York - Los Angeles",
      airline: flightCardData.airline || "Lufthansa",
      flightType: flightCardData.flightType || "One Way",
      flightDuration: flightCardData.flightDuration || "4h 05m",
      takeOff: flightCardData.takeOff || flightCardData.takeOffCode || "",
      landing: flightCardData.landing || flightCardData.landingCode || "",
      takeOffTime: flightCardData.takeOffTime || "",
      landingTime: flightCardData.landingTime || "",
      stops: flightCardData.stops || "Non-stop",
      stopDuration: flightCardData.stopDuration || "",
      fareType: flightCardData.fareType || "",
      cancellationFee: flightCardData.cancellationFee || "",
      flightChange: flightCardData.flightChange || "",
      seatsSequence: flightCardData.seatsSequence || "",
      inflightFeatures: flightCardData.inflightFeatures || "",
      taxesFees: flightCardData.taxesFees || "",
      type: flightCardData.type || "One Way Flight",
      rating: flightCardData.rating || 0,
      reviews: flightCardData.reviews || 0
    };

    localStorage.setItem('selectedFlightData', JSON.stringify(flightBookingData));

    // Navigate to flight-booking page
    router.push('/en/flight-booking');

    // Call onBookNow callback if provided
    if (onBookNow) {
      onBookNow(fareId);
    }
  };

  return (
   <div className="bg-white rounded-3xl shadow-lg p-6">
          <div className="mb-4">
            <span className="text-md text-[#24BDC7] uppercase tracking-wide font-bold">BESTSELLER</span>
            <div className="flex items-center mt-1 gap-2">
              <span className="text-sm text-gray-500">From </span>
               <span className="text-lg font-bold text-red-500">{price}</span>
              <span className="text-sm text-gray-500 line-through">{originalPrice}</span>
             
            </div>
          </div>

          {/* Journey Date */}
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-3xl">
              <div>
                <p className="text-xs text-gray-500 uppercase">Journey Date</p>
                <p className="font-semibold text-[#0C2C7A]">{journeyDate}</p>
                <p className="text-sm text-gray-600">{journeyDay}</p>
              </div>
              <div className="w-8 h-8 flex items-center justify-center">
                <span className="text-xs">
                   <FontAwesomeIcon size='xl' icon={faCalendarDays} className="text-[#24BDC7] text-sm" />
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-3xl">
              <div>
                <p className="text-xs text-gray-500 uppercase">Return Date</p>
                <p className="font-semibold text-[#0C2C7A]">{returnDate}</p>
                <p className="text-sm text-gray-600">{returnDay}</p>
              </div>
              <div className="w-8 h-8 rounded flex items-center justify-center">
                <span className="text-xs">
                <FontAwesomeIcon size='xl' icon={faCalendarDays} className="text-[#24BDC7] text-sm" />
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-3xl">
              <div>
                <p className="text-xs text-gray-500 uppercase">Passenger Class</p>
                <p className="font-semibold text-[#0C2C7A]">{passengers}</p>
                <p className="text-sm text-gray-600">{passengerClass}</p>
              </div>
              <div className="w-8 h-8 flex items-center justify-center">
                <span className="text-xs">
                  <User2 size={20} className="text-[#24BDC7]" />
                </span>
              </div>
            </div>
          </div>

          {/* Book Now Button */}
          <button
            onClick={handleBookNow}
            className="w-full bg-[#24BDC7] text-white py-3 px-6 rounded-3xl font-semibold hover:bg-[#1ea5ae] transition-colors mb-4"
          >
            Book Now
          </button>

          {/* Add to Wishlist */}
          <button
            onClick={handleWishlist}
            className={`w-full border border-[#24BDC7] py-3 px-6 rounded-3xl font-semibold transition-colors ${
              isWishlisted
                ? 'bg-[#24BDC7] text-white'
                : 'text-[#24BDC7] hover:bg-[#24BDC7] hover:text-white'
            }`}
          >
            {isWishlisted ? 'Added to Wishlist' : 'Add To Wishlist'}
          </button>

          {/* Stats */}
          <div className="flex justify-between text-center mt-6 pt-4 border-t border-gray-200">
            <div>
              <p className="text-sm font-semibold text-[#0C2C7A]">{views}</p>
            </div>
            <div>
              <p className="text-sm font-semibold text-[#0C2C7A]">{shares}</p>
            </div>
          </div>
        </div>
  );
};

export default BookingWidget;
