"use client";
import React from 'react';
import FlightCard from '../flight/flightCard';
import SectionBadge from '../ui/SectionBadge';
import ActionButton from '../ui/ActionButton';

const PopularFlightSection = ({ flights = [], regionName = "Popular Flights" }) => {
    if (!flights || flights.length === 0) {
        return (
            <section className="py-12 sm:py-16 lg:py-20">
                <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
                    <div className="text-center">
                        <SectionBadge text="FLIGHTS" isMobile={false} />
                        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#0C2C7A] dark:text-white mb-6">
                            {regionName} Flights
                        </h2>
                        <div className="bg-white rounded-lg shadow-sm p-12">
                            <div className="text-gray-500 text-lg mb-2">No flights available</div>
                            <p className="text-gray-400">Please check back later for flight options to this destination.</p>
                        </div>
                    </div>
                </div>
            </section>
        );
    }

    return (
        <section className="">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
                {/* Header */}
                {/* <div className="text-center mb-12">
                    <SectionBadge text="FLIGHTS" isMobile={false} />
                    <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#0C2C7A] dark:text-white mb-6">
                        {regionName} Flights
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                        Discover the best flight deals to {regionName}. Book now and save on your next adventure!
                    </p>
                </div> */}

                {/* Flight Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12 p-2">
                    {flights.map((flight) => (
                        <FlightCard
                            key={flight.id}
                            flightName={flight.flightName}
                            departure={`${flight.departureCity} (${flight.departureCode})`}
                            arrival={`${flight.arrivalCity} (${flight.arrivalCode})`}
                            rating={flight.rating}
                            reviews={flight.reviews}
                            duration={flight.duration}
                            features={flight.features}
                            price={flight.price}
                            image={flight.image}
                            badge={flight.badge}
                        />
                    ))}
                </div>

                {/* Call to Action */}
                {/* <div className="text-center">
                    <ActionButton text="View More Flights" isMobile={false} />
                </div> */}
            </div>
        </section>
    );
};

export default PopularFlightSection;
