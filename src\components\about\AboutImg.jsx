import Image from 'next/image';

const AboutImg = () => {
  return (
    <div className="relative flex flex-row gap-2 p-4 lg:p-12 items-center justify-center min-h-[400px] lg:min-h-[500px]">
      {/* First Image */}
      <div className="w-1/2  max-w-[140px] xs:max-w-[140px] lg:max-w-[300px] z-1">
        <Image
          src="/assets/img/about/01.jpg"
          alt="About Image 1"
          width={300}
          height={180}
          className="w-full h-auto rounded-full"
        />
      </div>
      
      {/* Blue border div behind badge */}
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[140px] h-[220px]  md:w-[160px] md:h-[250px] lg:w-[280px] lg:h-[450px] rounded-full border-2 xs:border-3 lg:border-8 border-[#24BDC7] border-double z-0"></div>
      
      {/* Badge in the middle */}
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-[#24BDC7] text-white w-[50px] h-[50px] xs:w-[60px] xs:h-[60px]  md:w-[120px] md:h-[120px] lg:w-[150px] lg:h-[150px] rounded-full flex flex-col items-center justify-center z-1 border-3 xs:border-2 lg:border-6 border-white text-center">
        <span className="text-xs xs:text-sm  md:text-xl lg:text-2xl xl:text-3xl font-bold leading-tight">30+</span>
        <span className="text-[0.3rem] xs:text-[0.35rem] text-xs md:text-sm lg:text-base font-bold leading-tight px-1">Years of Experience</span>
      </div>
      
      {/* Second Image */}
      <div className="w-1/2 max-w-[140px]  lg:max-w-[300px] mt-10  lg:mt-20 xl:mt-24 z-0">
        <Image
          src="/assets/img/about/02.jpg"
          alt="About Image 2"
          width={300}
          height={180}
          className="w-full h-auto rounded-full"
        />
      </div>
    </div>
  );
};

export default AboutImg;
