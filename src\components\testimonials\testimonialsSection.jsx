'use client';
import { useState, useEffect } from 'react';
import TestimonialCard from './testimonialsCard';
import SectionBadge from '../ui/SectionBadge';
import Testimonials from './testimonials';

function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(2); // Default to 2 for desktop

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      designation: 'Our Clients',
      image: '/assets/img/testimonial/01.jpg',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
    {
      id: 2,
      name: '<PERSON>',
      designation: 'Our Clients',
      image: '/assets/img/testimonial/02.jpg',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
    {
      id: 3,
      name: '<PERSON>',
      designation: 'Our Clients',
      image: '/assets/img/testimonial/03.jpg',
      rating: 5,
      review: 'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
    {
      id: 4,
      name: 'Michael Brown',
      designation: 'Our Clients',
      image: '/assets/img/testimonial/04.jpg',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
  ];

  // Update items per view based on screen size
  useEffect(() => {
    const updateItemsPerView = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth < 640) {
          setItemsPerView(1); // Mobile: 1 item
        } else if (window.innerWidth < 1024) {
          setItemsPerView(1); // Tablet: 1 item
        } else {
          setItemsPerView(2); // Desktop: 2 items
        }
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex >= testimonials.length - itemsPerView ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex <= 0 ? testimonials.length - itemsPerView : prevIndex - 1
    );
  };

  return (
    <section className="relative text-white pt-5 pb-10 bg-[url('/assets/img/testimonial/bg.jpg')] bg-fixed bg-cover bg-center overflow-hidden">
      {/* Dark overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/80 to-blue-800/80"></div>

      <Testimonials />
      {/* Background text at bottom */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 opacity-10 pointer-events-none">
        <div className="text-white text-3xl sm:text-5xl lg:text-6xl xl:text-[8rem] font-bold tracking-wider">
          TAVELO
        </div>
      </div>
    </section>
  );
}

export default TestimonialsSection;