import React from 'react';
import Header from "@/components/home/<USER>";

const PrivacyPolicy = () => {
  const breadcrumbItems = [
    { label: 'Privacy Policy', href: '/privacyPolicy' }
  ];

  return (
    <div className="bg-gray-100 min-h-screen">
      <Header
        backgroundImage="/assets/img/breadcrumb/01.jpg"
        height="min-h-[50vh]"
        heroTitle="Privacy Policy"
        heroSubtitle=""
        breadcrumbItems={breadcrumbItems}
        className="-mt-12"
      />
      <main className="blue-dark max-w-4xl mx-auto px-6 py-12 ">
        <section className="mb-8">
          <h2 className="text-3xl font-bold mb-4">Privacy Policy</h2>
          <p className="text-gray-600 leading-relaxed">
            Aenean ullamcorper est est, ac bibendum ipsum tincidunt vehicula. Nulla faucibus vulputate lorem, vitae placerat felis blandit ut. Nam sem quam, euismod sit amet augue et, mollis congue nisi. Vestibulum fringilla lobortis nunc ac tincidunt. Cras nec convallis quam. Maecenas non sem ut enim facilisis rhoncus. Sed odio ex, efficitur ac commodo sed, convallis vitae lectus. Aenean at urna ac tellus ullamcorper pretium. Aliquam erat volutpat. Aliquam sit amet tellus in tortor posuere convallis quis nec tellus. Nulla eu mauris sit amet enim eleifend congue. Quisque aliquam, turpis quis elementum tempus, velit arcu dignissim dui, a vehicula lectus nisi non felis.
          </p>
        </section>

        <section className="mb-8">
          <h2 className="text-3xl font-bold mb-4">Collect Information</h2>
          <p className="text-gray-600 leading-relaxed">
            Donec ac pulvinar diam, ac mollis augue. Etiam interdum fringilla magna, at placerat libero malesuada sed. Proin tincidunt a sapien at facilisis. Cras nec lectus pretium, convallis tellus eu, placerat augue. Curabitur luctus odio efficitur elit volutpat, quis venenatis tellus vestibulum. Nam ultrices massa id tellus commodo, at mollis elit mattis. Etiam eget ultrices lectus, at faucibus mauris. Integer at mauris ex. Vivamus interdum cursus mi quis venenatis. Sed pulvinar efficitur quam quis congue. Ut vel ornare lorem. Vivamus mi mi, vestibulum nec eleifend eu, lobortis ac neque. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed hendrerit augue dui, non rutrum enim ultrices vel. Fusce mattis ullamcorper nisl, sit amet venenatis odio tincidunt eget.
          </p>
          <ul className="text-gray-600 list-disc list-inside mt-4">
            <li>Personal data and contact details</li>
            <li>Usage data and browsing history</li>
          </ul>
        </section>

        <section className="mb-8">
          <h2 className="text-3xl font-bold mb-4">Usage of Information</h2>
          <p className="text-gray-600 leading-relaxed">
            Phasellus commodo venenatis erat, et vestibulum mi fringilla in. Proin elit urna, condimentum ut elit id, imperdiet rutrum orci. Praesent vehicula velit at est rutrum lacinia. Nullam accumsan at tortor in ullamcorper. Proin semper sagittis nisl, vitae finibus nisl maximus non. Cras dictum risus quis augue tempor egestas. Proin luctus fermentum nunc, eget pretium dolor tristique id.
            <br /><br />
            Suspendisse hendrerit ex sit amet augue lobortis ullamcorper. Maecenas dignissim facilisis orci, non imperdiet sapien ornare at. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.
            <br /><br />
            Nam ultrices mi mauris, eget tempus massa ornare id. Aenean rhoncus vestibulum diam, ut dapibus dolor vehicula non. Proin rhoncus convallis commodo.
          </p>
        </section>

        <section className="mb-8">
          <h2 className="text-3xl font-bold mb-4">Security of User Data</h2>
          <p className="text-gray-600 leading-relaxed">
            Integer justo neque imperdiet vitae consequat in vehicula quis dolor orbi lorem leo volutpat a tristique:
          </p>
          <ol className="text-gray-600 list-decimal list-inside mt-4">
            <li>Ut scelerisque hendrerit venenatis</li>
            <li>Proin fermentum lacus nec augue blandit placerat</li>
            <li>Ut vestibulum elit justo suscipit sem ultricies</li>
            <li>Integer fermentum vitae magna in condimentum</li>
            <li>Aenean ultrices neque id pellentesque tincidunt</li>
            <li>Donec ut vestibulum sem, in faucibus mauris</li>
          </ol>
        </section>

        <section className="mb-8">
          <h2 className="text-3xl font-bold mb-4">Copyright and Security</h2>
          <p className="text-gray-600 leading-relaxed">
            Vestibulum bibendum metus quis purus sagittis ultricies. Vestibulum fringilla urna volutpat eros pharetra consectetur. Integer rutrum eu odio et pulvinar. Sed hendrerit pellentesque faucibus. In venenatis lacus sit amet vehicula efficitur. Suspendisse pulvinar malesuada dui non mollis. Aliquam urna massa, rutrum vel luctus in, facilisis a turpis. Ut aliquet accumsan turpis, eget egestas sem pellentesque nec. Phasellus faucibus congue tempor. Mauris ac massa scelerisque metus pulvinar feugiat in ut leo. Proin congue felis orci. Suspendisse consectetur nisl at faucibus venenatis. Quisque pretium rhoncus dui, porttitor varius mi iaculis nec.
          </p>
        </section>
      </main>
    </div>
  );
};

export default PrivacyPolicy;