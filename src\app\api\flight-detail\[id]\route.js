import { NextResponse } from 'next/server';

/**
 * GET /api/flight-detail/[id]
 * Fetch flight detail by ID
 */
export async function GET(request, { params }) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: 'Flight ID is required' },
        { status: 400 }
      );
    }

    // Example: Replace this with your actual API call to your backend
    // const response = await fetch(`${process.env.BACKEND_API_URL}/flights/${id}`, {
    //   headers: {
    //     'Authorization': `Bearer ${process.env.API_TOKEN}`,
    //     'Content-Type': 'application/json',
    //   },
    // });

    // For demo purposes, return sample data based on ID
    const sampleFlightData = {
      "id": id,
      "currency": "USD",
      "segments": [
          {
              "flightNumber": "LH7399",
              "airlineCode": "LH",
              "airlineDisplayName": "Lufthansa",
              "airlineLogo": "https://example.com/logos/lufthansa.png",
              "airlineRating": 4.2,
              "reviews": 1150,
              "passengers": {
                  "adults": 5,
                  "children": 4
              },
              "images": [
                  "https://live.themewild.com/tavelo/assets/img/flight/single-1.jpg",
                  "https://live.themewild.com/tavelo/assets/img/flight/single-2.jpg",
                  "https://live.themewild.com/tavelo/assets/img/flight/single-3.jpg"
              ],
              "duration": "5h 39m",
              "departure": {
                  "city": "New York",
                  "cityCode": "JFK",
                  "countryCode": "US",
                  "countryName": "United States",
                  "scheduledDeparture": "2025-12-18T00:00:00.000Z"
              },
              "arrival": {
                  "city": "Reykjavik",
                  "cityCode": "KEF",
                  "countryCode": "IS",
                  "countryName": "Iceland",
                  "scheduledArrival": "2025-12-18T05:39:00.000Z"
              }
          }
      ],
      "serviceClass": "FIRST",
      "baggageAllowance": "Cabin: 10kg, Check-in: 30kg",
      "refundable": "Partially Refundable",
      "price": 3183,
      "discountRate": 0.14,
      "tax": 420.16,
      "totalPrice": 3157.54,
      "flightType": "oneWay",
      "legs": [
          {
              "from": "JFK",
              "to": "KEF",
              "date": "2025-12-18"
          }
      ],
      "status": "available"
    };

    // Simulate different responses based on ID
    if (id === 'not-found') {
      return NextResponse.json(
        { error: 'Flight not found' },
        { status: 404 }
      );
    }

    if (id === 'error') {
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    // Return the flight data
    return NextResponse.json(sampleFlightData);

  } catch (error) {
    console.error('Error fetching flight detail:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Example of how to integrate with your actual backend API:
 * 
 * export async function GET(request, { params }) {
 *   try {
 *     const { id } = params;
 *     
 *     // Call your backend API
 *     const response = await fetch(`${process.env.BACKEND_API_URL}/flight-detail/${id}`, {
 *       headers: {
 *         'Authorization': `Bearer ${process.env.API_TOKEN}`,
 *         'Content-Type': 'application/json',
 *       },
 *     });
 *     
 *     if (!response.ok) {
 *       throw new Error(`Backend API error: ${response.status}`);
 *     }
 *     
 *     const flightData = await response.json();
 *     return NextResponse.json(flightData);
 *     
 *   } catch (error) {
 *     console.error('Error:', error);
 *     return NextResponse.json(
 *       { error: 'Failed to fetch flight details' },
 *       { status: 500 }
 *     );
 *   }
 * }
 */
