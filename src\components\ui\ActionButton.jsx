import React from 'react';
import { ArrowRight } from 'lucide-react';

const ActionButton = ({ 
  text, 
  onClick, 
  isMobile = false, 
  variant = "primary",
  className = "",
  ...props 
}) => {
  const baseClasses = "bg-[#24BDC7] hover:bg-[#1ea8b2] text-white font-semibold rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center gap-2";
  
  const sizeClasses = isMobile 
    ? "py-3 sm:py-4 px-6 sm:px-8 text-sm sm:text-base" 
    : "py-3 px-6";
  
  const layoutClasses = isMobile ? "mx-auto" : "";

  return (
    <button 
      className={`${baseClasses} ${sizeClasses} ${layoutClasses} ${className}`}
      onClick={onClick}
      {...props}
    >
      {text}
      <ArrowRight
        className="bg-white rounded-full p-0.5 text-[#24BDC7]"
        size={20}
      />
    </button>
  );
};

export default ActionButton;
