const TeamCard = ({ name, designation, image }) => {
    return (
        <div className="flex flex-col items-center text-center w-full max-w-xs mx-auto h-full">
            <div className="flex-shrink-0 mb-4 relative">
                <img
                    src={image}
                    alt={`${name} profile`}
                    className="w-36 h-36 sm:w-40 sm:h-40 md:w-48 md:h-48 rounded-full object-cover mx-auto"
                />

                <div className="absolute bottom-[-1.5rem] px-3 sm:px-4 py-2 mb-4 bg-white shadow-md rounded-full w-full flex flex-row justify-between items-center gap-2 sm:gap-4 md:gap-8 mt-4">
                    <div className="flex flex-col items-start flex-1 min-w-0">
                        <h4 className="font-semibold text-xs sm:text-sm md:text-base text-gray-800 truncate w-full text-left">{name}</h4>
                        <p className="text-teal-500 text-xs sm:text-sm font-medium truncate w-full text-left">{designation}</p>
                    </div>
                    <div className="flex-shrink-0">
                        <button className="text-teal-500 hover:text-teal-700 transition-colors duration-200">
                            <svg
                                className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                                />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TeamCard;