import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const CheckboxItem = ({ label, count, checked, onChange, icon, className = '' }) => (
  <label className={`flex items-center justify-between cursor-pointer ${className}`}>
    <div className="flex items-center gap-2">
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        className="sr-only"
      />
      <div className={`h-4 w-4 border border-[#0C2C82] rounded-[5px] flex items-center justify-center ${checked ? 'bg-[#24BDC7] border-[#24BDC7]' : 'bg-white'}`}>
        {checked && <span className="text-white font-bold text-[9px]">✓</span>}
      </div>
      {icon && <FontAwesomeIcon icon={icon} className="text-[#0C2C7A] ml-3 mr-2" />}
      <span className="text-sm font-medium">{label}</span>
    </div>
    {count && (
      <span className="text-xs font-medium px-2 py-1 rounded">
        ({count})
      </span>
    )}
  </label>
);

export default CheckboxItem;
