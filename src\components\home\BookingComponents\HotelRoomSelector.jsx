// components/home/<USER>/HotelRoomSelector.jsx
import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers } from '@fortawesome/free-solid-svg-icons';
import bookingConfigData from '../../../app/data/bookingConfigData.json';

const HotelRoomSelector = ({
  rooms = [{ adults: bookingConfigData.defaultPassengers.adults, children: bookingConfigData.defaultPassengers.children, roomType: bookingConfigData.defaultRoomType }],
  onRoomsChange
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [tempRooms, setTempRooms] = useState(rooms);
  const containerRef = useRef(null);

  // Room type options from config
  const roomTypes = bookingConfigData.roomTypes.map(type => ({
    value: type,
    label: type
  }));

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Update temp state when props change
  useEffect(() => {
    setTempRooms(rooms);
  }, [rooms]);

  const updateRoomGuests = (roomIndex, type, change) => {
    setTempRooms(prevRooms => {
      const newRooms = [...prevRooms];
      const currentCount = newRooms[roomIndex][type] || 0;
      const newCount = Math.max(0, currentCount + change);
      
      // Validate limits
      if (type === 'adults' && newCount < 1) return prevRooms; // At least 1 adult per room
      if (type === 'adults' && newCount > 6) return prevRooms; // Max 6 adults per room
      if (type === 'children' && newCount > 4) return prevRooms; // Max 4 children per room

      newRooms[roomIndex] = {
        ...newRooms[roomIndex],
        [type]: newCount
      };

      return newRooms;
    });
  };

  const updateRoomType = (roomIndex, roomType) => {
    setTempRooms(prevRooms => {
      const newRooms = [...prevRooms];
      newRooms[roomIndex] = {
        ...newRooms[roomIndex],
        roomType
      };
      return newRooms;
    });
  };

  const addRoom = () => {
    if (tempRooms.length < 8) { // Max 8 rooms
      setTempRooms(prev => [
        ...prev,
        { adults: 2, children: 0, roomType: 'Double Room' }
      ]);
    }
  };

  const removeRoom = (roomIndex) => {
    if (tempRooms.length > 1) {
      setTempRooms(prev => prev.filter((_, index) => index !== roomIndex));
    }
  };

  const handleApply = () => {
    if (onRoomsChange) {
      onRoomsChange(tempRooms);
    }
    setIsOpen(false);
  };

  const handleCancel = () => {
    setTempRooms(rooms);
    setIsOpen(false);
  };

  const totalRooms = rooms.length;
  const totalGuests = rooms.reduce((sum, room) => sum + room.adults + room.children, 0);
  const displayText = `${totalRooms} Room${totalRooms > 1 ? 's' : ''}, ${totalGuests} Guest${totalGuests > 1 ? 's' : ''}`;

  return (
    <div className="col-span-12 lg:col-span-3 text-[#0C2C7A]" ref={containerRef}>
      <div className={`relative bg-[#E4F7F8] pb-3 rounded-lg p-1 border-2 transition-colors min-h-[4rem] flex flex-col justify-between ${
        isFocused ? 'border-[#24BDC7]' : 'border-[#E4F7F8]'
      }`}>
        <div className="flex items-center justify-between">
          <label className="block text-xs font-medium mb-0.5">Rooms, Guests</label>
          <FontAwesomeIcon icon={faUsers} className="text-[#24BDC7] text-sm" />
        </div>

        {/* Display current selection */}
        <div
          className="flex-1 flex items-center justify-between cursor-pointer rounded-lg p-1 transition-colors"
          onClick={() => setIsOpen(!isOpen)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setTimeout(() => setIsFocused(false), 150)}
          tabIndex={0}
        >
          <div>
            <div className="text-sm lg:text-base font-semibold">{displayText}</div>
          </div>
        </div>

        {/* Dropdown panel */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-3 max-h-96 overflow-y-auto">
            {tempRooms.map((room, roomIndex) => (
              <div key={roomIndex} className="mb-4 p-3 border border-gray-100 rounded-lg">
                {/* Room Header */}
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-semibold text-[#0C2C7A]">Room {roomIndex + 1}</h4>
                  {tempRooms.length > 1 && (
                    <button
                      onClick={() => removeRoom(roomIndex)}
                      className="text-red-500 hover:text-red-700 p-1"
                    >
                    </button>
                  )}
                </div>

                {/* Room Type Selection */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Room Type</label>
                  <div className="grid grid-cols-2 gap-2">
                    {roomTypes.map((type) => (
                      <button
                        key={type.value}
                        onClick={() => updateRoomType(roomIndex, type.value)}
                        className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                          room.roomType === type.value
                            ? 'bg-[#24BDC7] text-white border-[#24BDC7]'
                            : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'
                        }`}
                      >
                        {type.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Guest Selection */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-700">Guests</label>

                  {/* Adults */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Adults</div>
                      <div className="text-xs text-gray-500">12+ years</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <button
                        onClick={() => updateRoomGuests(roomIndex, 'adults', -1)}
                        disabled={room.adults <= 1}
                        className="w-6 h-6 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"/>
                        </svg>
                      </button>
                      <span className="w-6 text-center font-medium">{room.adults}</span>
                      <button
                        onClick={() => updateRoomGuests(roomIndex, 'adults', 1)}
                        disabled={room.adults >= 6}
                        className="w-6 h-6 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"/>
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Children */}
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Children</div>
                      <div className="text-xs text-gray-500">2-11 years</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <button
                        onClick={() => updateRoomGuests(roomIndex, 'children', -1)}
                        disabled={room.children <= 0}
                        className="w-6 h-6 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"/>
                        </svg>
                      </button>
                      <span className="w-6 text-center font-medium">{room.children}</span>
                      <button
                        onClick={() => updateRoomGuests(roomIndex, 'children', 1)}
                        disabled={room.children >= 4}
                        className="w-6 h-6 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Add Room Button */}
            {tempRooms.length < 8 && (
              <button
                onClick={addRoom}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 mb-4 text-[#24BDC7] bg-[#E4F7F8] hover:bg-[#24BDC7] hover:text-white rounded-lg transition-colors font-medium"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"/>
                </svg>
                Add Another Room
              </button>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4 border-t border-gray-200">
              <button
                onClick={handleCancel}
                className="flex-1 px-4 py-2 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleApply}
                className="flex-1 px-4 py-2 text-white bg-[#24BDC7] hover:bg-teal-600 rounded-lg transition-colors"
              >
                Apply
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HotelRoomSelector;
