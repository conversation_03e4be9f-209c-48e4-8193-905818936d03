"use client"
import React from 'react';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Clock, Plane, MapPin, Calendar, Users, Wifi, Coffee, Monitor } from 'lucide-react';
import ActionButton from '../ui/ActionButton';

export default function FlightDetails() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const flightId = searchParams.get('id');

  // Mock flight data - in real app, this would come from API
  const flightData = {
    id: flightId || '1',
    airline: 'Emirates',
    flightNumber: 'EK 203',
    from: 'New York (JFK)',
    to: 'Dubai (DXB)',
    departureTime: '10:30 AM',
    arrivalTime: '6:45 AM',
    duration: '14h 15m',
    price: 1250,
    aircraft: 'Boeing 777-300ER',
    class: 'Economy',
    baggage: '23kg checked + 7kg carry-on',
    amenities: ['WiFi', 'Entertainment', 'Meals', 'USB Charging'],
    stops: 0,
    date: '2025-09-15'
  };

  const handleBookNow = () => {
    router.push(`/flight-booking?id=${flightData.id}`);
  };

  const handleBackToList = () => {
    router.push('/flight-list');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <button
          onClick={handleBackToList}
          className="flex items-center gap-2 text-[#24BDC7] hover:text-[#1da8b3] mb-6 transition-colors"
        >
          <ArrowLeft size={20} />
          Back to Flight List
        </button>

        {/* Flight Details Card */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-[#24BDC7] rounded-full flex items-center justify-center">
                <Plane className="text-white" size={24} />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-800">{flightData.airline}</h2>
                <p className="text-gray-600">{flightData.flightNumber}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-[#24BDC7]">${flightData.price}</p>
              <p className="text-sm text-gray-600">per person</p>
            </div>
          </div>

          {/* Flight Route */}
          <div className="flex items-center justify-between mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-800">{flightData.departureTime}</div>
              <div className="text-sm text-gray-600">{flightData.from}</div>
            </div>

            <div className="flex-1 mx-4 relative">
              <div className="flex items-center">
                <div className="flex-1 border-t-2 border-gray-300"></div>
                <Plane className="text-[#24BDC7] mx-2" size={20} />
                <div className="flex-1 border-t-2 border-gray-300"></div>
              </div>
              <div className="text-center mt-2">
                <div className="text-sm font-medium text-gray-800">{flightData.duration}</div>
                <div className="text-xs text-gray-600">Non-stop</div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-gray-800">{flightData.arrivalTime}</div>
              <div className="text-sm text-gray-600">{flightData.to}</div>
            </div>
          </div>

          {/* Flight Info Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <Calendar className="text-[#24BDC7] mb-2" size={20} />
              <div className="text-sm text-gray-600">Date</div>
              <div className="font-medium">{flightData.date}</div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <Plane className="text-[#24BDC7] mb-2" size={20} />
              <div className="text-sm text-gray-600">Aircraft</div>
              <div className="font-medium">{flightData.aircraft}</div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <Users className="text-[#24BDC7] mb-2" size={20} />
              <div className="text-sm text-gray-600">Class</div>
              <div className="font-medium">{flightData.class}</div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <MapPin className="text-[#24BDC7] mb-2" size={20} />
              <div className="text-sm text-gray-600">Baggage</div>
              <div className="font-medium">{flightData.baggage}</div>
            </div>
          </div>

          {/* Amenities */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">Flight Amenities</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {flightData.amenities.map((amenity, index) => (
                <div key={index} className="flex items-center gap-2">
                  {amenity === 'WiFi' && <Wifi className="text-[#24BDC7]" size={16} />}
                  {amenity === 'Entertainment' && <Monitor className="text-white" size={16} />}
                  {amenity === 'Meals' && <Coffee className="text-[#24BDC7]" size={16} />}
                  {amenity === 'USB Charging' && <div className="w-4 h-4 bg-[#24BDC7] rounded-full"></div>}
                  <span className="text-sm">{amenity}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Book Now Button */}
          <div className="flex justify-center">
            <ActionButton
              onClick={handleBookNow}
              className="bg-[#24BDC7] hover:bg-[#1da8b3] text-white px-8 py-3 rounded-lg font-semibold"
            >
              Book This Flight
            </ActionButton>
          </div>
        </div>
      </div>
    </div>
  );
}
