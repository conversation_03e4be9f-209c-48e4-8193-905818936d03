// components/home/<USER>/CheckInDateSelector.jsx
import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCalendarDays } from '@fortawesome/free-solid-svg-icons';

// Custom Calendar Component
const CustomCalendar = ({ selectedDate, onDateSelect, minDate, onClose }) => {
  const [currentMonth, setCurrentMonth] = useState(selectedDate || new Date());
  const calendarRef = useRef(null);

  // Close calendar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target)) {
        if (onClose) onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    return days;
  };

  const navigateMonth = (direction) => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + direction);
      return newMonth;
    });
  };

  const handleDateClick = (date) => {
    if (date && (!minDate || date >= minDate)) {
      if (onDateSelect) {
        onDateSelect(date);
      }
      if (onClose) onClose();
    }
  };

  const isToday = (date) => {
    if (!date) return false;
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSelected = (date) => {
    if (!date || !selectedDate) return false;
    return date.toDateString() === selectedDate.toDateString();
  };

  const isDisabled = (date) => {
    if (!date) return true;
    return minDate && date < minDate;
  };

  const days = getDaysInMonth(currentMonth);

  return (
    <div
      ref={calendarRef}
      className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-xl z-50 w-64"
    >
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-2 border-b border-gray-200">
        <button
          onClick={() => navigateMonth(-1)}
          className="p-1 hover:bg-gray-100 rounded-md transition-colors"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"/>
          </svg>
        </button>

        <div className="text-base font-semibold text-[#0C2C7A]">
          {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
        </div>

        <button
          onClick={() => navigateMonth(1)}
          className="p-1 hover:bg-gray-100 rounded-md transition-colors"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
          </svg>
        </button>
      </div>

      {/* Day Names */}
      <div className="grid grid-cols-7 gap-0.5 p-2">
        {dayNames.map(day => (
          <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-0.5 p-2 pt-0">
        {days.map((date, index) => (
          <button
            key={index}
            onClick={() => handleDateClick(date)}
            disabled={isDisabled(date)}
            className={`
              h-8 w-8 text-xs font-medium rounded-md transition-all relative
              ${!date
                ? 'invisible'
                : isSelected(date)
                  ? 'bg-[#24BDC7] text-white shadow-md'
                  : isToday(date)
                    ? 'bg-[#E4F7F8] text-[#24BDC7] border border-[#24BDC7]'
                    : isDisabled(date)
                      ? 'text-gray-300 cursor-not-allowed'
                      : 'text-gray-700 hover:bg-[#E4F7F8] hover:text-[#24BDC7]'
              }
            `}
          >
            {date ? date.getDate() : ''}
          </button>
        ))}
      </div>

      {/* Today Button */}
      <div className="p-2 border-t border-gray-200">
        <button
          onClick={() => handleDateClick(new Date())}
          className="w-full py-1.5 px-3 bg-[#E4F7F8] text-[#24BDC7] rounded-md hover:bg-[#24BDC7] hover:text-white transition-colors text-sm font-medium"
        >
          Today
        </button>
      </div>
    </div>
  );
};

const CheckInDateSelector = ({
  checkInDate,
  onDateChange
}) => {
  const [showCalendar, setShowCalendar] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // Helper function to format date as DD/MM/YYYY
  const formatDate = (date) => {
    if (!date) return 'Select Date';
    const d = new Date(date);
    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
  };

  // Default dates if not provided
  const today = new Date();
  const checkIn = checkInDate || today;

  const handleDateSelect = (date) => {
    if (onDateChange) {
      onDateChange('checkIn', date);
    }
    setShowCalendar(false);
  };

  const openCalendar = () => {
    setShowCalendar(true);
  };

  return (
    <div className="col-span-12 lg:col-span-3 text-[#0C2C7A]">
      <div className={`relative bg-[#E4F7F8] rounded-lg p-1 border-2 transition-colors min-h-[4rem] flex flex-col justify-between ${
        isFocused ? 'border-[#24BDC7]' : 'border-[#E4F7F8]'
      }`}>
        <div className="flex items-center justify-between">
          <label className="block text-xs font-medium mb-0.5">Check In</label>
          <FontAwesomeIcon icon={faCalendarDays} className="text-[#24BDC7] text-sm" />
        </div>

        {/* Clickable date display */}
        <div
          className="flex-1 flex items-center justify-between cursor-pointer rounded-lg p-1 transition-colors"
          onClick={openCalendar}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setTimeout(() => setIsFocused(false), 150)}
          tabIndex={0}
        >
          <div>
            <div className="text-sm lg:text-base font-semibold truncate">{formatDate(checkIn)}</div>
          </div>

          {/* Custom Calendar */}
          {showCalendar && (
            <CustomCalendar
              selectedDate={checkIn}
              onDateSelect={handleDateSelect}
              minDate={today}
              onClose={() => setShowCalendar(false)}
            />
          )}
        </div>
        <div className="h-2"></div>
      </div>
    </div>
  );
};

export default CheckInDateSelector;
