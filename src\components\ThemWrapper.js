'use client';

import { useEffect } from 'react';
import { useSelector } from 'react-redux';
export default function ThemeWrapper({ children }) {
  const mode = useSelector((state) => state.theme.mode);

 useEffect(() => {
  console.log('🔁 Theme Effect Fired');
  console.log('Redux theme mode:', mode);

  const html = document.documentElement;
  console.log('Before toggle:', html.className);

  if (mode === 'dark') {
    html.classList.add('dark');
  } else {
    html.classList.remove('dark');
  }

  console.log('After toggle:', html.className);
}, [mode]);


  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 text-black dark:text-white transition-colors duration-300">
      <main className="">{children}</main>
    </div>
  );
}
