// components/home/<USER>/LocationSelector.jsx
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlaneDeparture, faPlaneArrival } from '@fortawesome/free-solid-svg-icons';
import { searchCities } from '../../../redux/slices/citySlice';
import { debounce } from '../../../utils/debounce';

const LocationSelector = ({
  label,
  location,
  onLocationChange
}) => {
  const dispatch = useDispatch();
  const { cities, loading } = useSelector((state) => state.cities);
  
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeInput, setActiveInput] = useState(''); // 'city' or 'airport'
  const containerRef = useRef(null);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Debounced API search function
  const debouncedSearch = useCallback(
    debounce((query) => {
      if (query && query.length >= 2) {
        dispatch(searchCities(query));
        setShowSuggestions(true);
      } else {
        setShowSuggestions(false);
      }
    }, 300),
    [dispatch]
  );

  const handleCityChange = (e) => {
    const value = e.target.value;
    if (onLocationChange) {
      onLocationChange({
        ...location,
        city: value
      });
    }
    setActiveInput('city');
    
    // Trigger debounced search
    debouncedSearch(value);
  };

  const handleSuggestionClick = (suggestion) => {
    if (onLocationChange) {
      onLocationChange({
        city: suggestion.displayName,
        airport: `${suggestion.cityCode} - ${suggestion.displayName}`,
        cityCode: suggestion.cityCode,
        countryCode: suggestion.countryCode,
        countryName: suggestion.countryName
      });
    }
    setShowSuggestions(false);
  };

  const handleInputFocus = (inputType) => {
    setIsFocused(true);
    setActiveInput(inputType);
    // Show suggestions if there's already text and we have cities data
    if (inputType === 'city' && location.city && cities.length > 0) {
      setShowSuggestions(true);
    }
  };

  return (
    <div className="col-span-12 sm:col-span-6 lg:col-span-3 text-[#0C2C7A]" ref={containerRef}>
      <div className={`relative bg-[#E4F7F8] rounded-xl p-1 pb-3 border-2 transition-colors min-h-[4rem] flex flex-col justify-between ${
        isFocused ? 'border-[#24BDC7]' : 'border-[#E4F7F8]'
      }`}>
        <div className="flex items-center justify-between">
          <label className="block text-xs font-medium mb-0.5">{label}</label>
          <FontAwesomeIcon
            icon={label.toLowerCase().includes('from') ? faPlaneDeparture : faPlaneArrival}
            className="text-[#24BDC7] text-sm"
          />
        </div>

        {/* City Input */}
        <div className="flex-1 flex items-center justify-center relative">
          <input
            type="text"
            value={location.city || ''}
            onChange={handleCityChange}
            onFocus={() => handleInputFocus('city')}
            onBlur={() => setTimeout(() => setIsFocused(false), 150)}
            placeholder="Enter city name"
            className="w-full bg-transparent text-sm lg:text-base font-semibold text-[#0C2C7A] placeholder-gray-400 outline-none rounded p-1 transition-colors"
          />
        </div>

        {/* Suggestions Dropdown */}
        {showSuggestions && (
          <div className="absolute top-full left-0 right-0 mt-0.5 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-48 lg:max-h-64 overflow-y-auto">
            {loading ? (
              <div className="px-3 lg:px-4 py-3 text-center text-gray-500">
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-[#24BDC7]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Searching cities...
                </div>
              </div>
            ) : cities.length > 0 ? (
              cities.map((suggestion, index) => (
                <div
                  key={`${suggestion.cityCode}-${index}`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="px-3 lg:px-4 py-1 lg:py-2 hover:bg-[#E4F7F8] cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="text-base lg:text-lg font-semibold text-[#0C2C7A] truncate">
                        {suggestion.displayName}
                      </div>
                      <div className="text-xs text-gray-600 truncate">
                        {suggestion.countryName}
                      </div>
                    </div>
                    <div className="text-xs lg:text-sm font-medium text-[#24BDC7] bg-[#E4F7F8] px-1.5 py-0.5 rounded ml-2 flex-shrink-0">
                      {suggestion.cityCode}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-3 lg:px-4 py-3 text-center text-gray-500">
                No cities found
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default LocationSelector;
