"use client";
import React from 'react';
import { Phone, Mail } from 'lucide-react';

const ContactCard = ({
  title = "",
  description = "",
  phone = "",
  email = ""
}) => {
  return (
    <div className="bg-white rounded-3xl shadow-lg  p-6">
      <h3 className="text-xl font-bold text-[#0C2C7A] mb-3">{title}</h3>
      
      <p className="text-gray-600 text-sm mb-6 leading-relaxed">
        {description}
      </p>
      
      <div className="space-y-4">
        {/* Phone */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-[#24BDC7] rounded-full rounded-bl-none flex items-center justify-center flex-shrink-0">
            <Phone size={16} className="text-white" />
          </div>
          <a 
            href={`tel:${phone}`}
            className="text-[#0C2C7A] font-medium hover:text-[#24BDC7] transition-colors"
          >
            {phone}
          </a>
        </div>
        
        {/* Email */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-[#24BDC7] rounded-full rounded-bl-none flex items-center justify-center flex-shrink-0">
            <Mail size={16} className="text-white" />
          </div>
          <a 
            href={`mailto:${email}`}
            className="text-[#0C2C7A] font-medium hover:text-[#24BDC7] transition-colors"
          >
            {email}
          </a>
        </div>
      </div>
    </div>
  );
};

export default ContactCard;
