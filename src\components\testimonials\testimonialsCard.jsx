import Image from 'next/image';

const TestimonialCard = ({ name, designation, image, review, rating }) => {
  const renderStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <svg
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-[#25BDC7]' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <div className="relative bg-white lg:rounded-full md:rounded-full rounded-3xl p-6 md:p-12 sm:p-4 lg:p-8 w-full max-w-xs sm:max-w-xs lg:max-w-lg md:max-w-xl mx-auto">
      {/* Quote Icon */}
      <div className="absolute top-6 right-6 sm:top-4 sm:right-8 md:right-20 lg:top-6 lg:right-12 w-8 h-8 sm:w-16 sm:h-16 lg:w-12 lg:h-16 md:h-20 md:w-20 opacity-20">
        <img
          src="/assets/img/icon/quote.svg"
          alt="Quote"
          className="w-full h-full"
        />
      </div>

      {/* Profile Section */}
      <div className="flex flex-col sm:flex-row items-center sm:items-start gap-2 sm:gap-2 mb-4">
        <div className="relative flex-shrink-0 lg:pl-8">
          <img
            src={image}
            alt={name}
            className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-full object-cover z-10"
          />
        </div>

        <div className="flex-1 text-center sm:text-left">
          <h3 className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mb-1">
            {name}
          </h3>
          <p className="text-xs sm:text-sm text-[#25BDC7] font-medium mb-2 sm:mb-3">
            {designation}
          </p>
        </div>
      </div>

      {/* Review Text */}
      <p className="text-gray-600 text-xs sm:text-sm leading-relaxed mb-3 sm:mb-4 px-2 sm:px-4 lg:px-8 text-center sm:text-left">
        {review}
      </p>

      {/* Rating Stars */}
      <div className="flex gap-1 justify-center sm:justify-start px-2 sm:px-4 lg:px-8">
        {renderStars(rating)}
      </div>
    </div>
  );
};

export default TestimonialCard;