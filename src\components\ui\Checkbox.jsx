import React from 'react';
import { Check } from 'lucide-react';

const Checkbox = ({ 
  checked = false,
  onChange,
  label,
  required = false,
  className = "",
  error,
  ...props 
}) => {
  return (
    <div className={`flex items-start gap-3 ${className}`}>
      <div className="relative flex-shrink-0 mt-0.5">
        <input
          type="checkbox"
          checked={checked}
          onChange={onChange}
          required={required}
          className="sr-only"
          {...props}
        />
        <div 
          className={`w-5 h-5 border-2 rounded cursor-pointer transition-all duration-200 flex items-center justify-center ${
            checked 
              ? 'bg-[#24BDC7] border-[#24BDC7]' 
              : error 
                ? 'border-red-500 bg-red-50'
                : 'border-gray-300 bg-white hover:border-[#24BDC7]'
          }`}
          onClick={() => onChange && onChange({ target: { checked: !checked } })}
        >
          {checked && (
            <Check className="w-3 h-3 text-white" strokeWidth={3} />
          )}
        </div>
      </div>
      
      {label && (
        <label 
          className={`text-sm cursor-pointer ${error ? 'text-red-500' : 'text-gray-600'}`}
          onClick={() => onChange && onChange({ target: { checked: !checked } })}
        >
          {label}
        </label>
      )}
      
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};

export default Checkbox;
