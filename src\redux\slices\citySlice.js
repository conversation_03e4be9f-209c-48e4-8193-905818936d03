// src/redux/slices/citySlice.js
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { fetchCities } from "@/api/cityApi";

export const searchCities = createAsyncThunk(
  "cities/searchCities",
  async (query, { rejectWithValue }) => {
    try {
      const res = await fetchCities(query);
      console.log("Fetched cities data:", res.data); // Added console log to see fetched data
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data?.message || "Failed to fetch cities");
    }
  }
);

const citySlice = createSlice({
  name: "cities",
  initialState: {
    cities: [],
    totalCount: 0,
    totalPages: 0,
    currentPage: 1,
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(searchCities.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchCities.fulfilled, (state, action) => {
        state.loading = false;
        console.log("Action payload in fulfilled:", action.payload); // Added console log to see payload
        state.cities = action.payload.cities;
        state.totalCount = action.payload.totalCount;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
      })
      .addCase(searchCities.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default citySlice.reducer;
