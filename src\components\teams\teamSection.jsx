import { useState, useEffect } from 'react';
import TeamCard from './teamCard';
import SectionBadge from '../ui/SectionBadge';

export default function TeamSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(4);

  const teamMembers = [
    {
      id: 1,
      name: "<PERSON>",
      designation: "Founder & Director",
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 2,
      name: "<PERSON>",
      designation: "Designer",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 3,
      name: "<PERSON>",
      designation: "Developer",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 4,
      name: "<PERSON>",
      designation: "Manager",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 5,
      name: "Edna Craig",
      designation: "Head Of Design",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 6,
      name: "Audrey Gadis",
      designation: "Sales Support",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    },
  ];

  // Update items per view based on screen size
  useEffect(() => {
    const updateItemsPerView = () => {
      if (window.innerWidth < 640) {
        setItemsPerView(1); // Mobile: 1 item
      } else if (window.innerWidth < 1024) {
        setItemsPerView(2); // Tablet: 2 items
      } else {
        setItemsPerView(4); // Desktop: 4 items
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex >= teamMembers.length - itemsPerView ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex <= 0 ? teamMembers.length - itemsPerView : prevIndex - 1
    );
  };

  return (
    <section className="bg-white relative py-5">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 md:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#0C2C7A] my-5">
            Meet With Our Experts Team
          </h2>
        </div>

        {/* Team Carousel */}
        <div className="relative max-w-6xl mx-auto">
          <div className="flex justify-center items-center">
            {/* Previous Button - Hidden on mobile */}
            <button
              onClick={prevTestimonial}
              className="hidden sm:flex w-10 md:w-12 h-10 md:h-12 bg-gray-100 hover:bg-gray-200 rounded-full items-center justify-center text-gray-600 transition-all duration-200 shadow-lg mr-4 md:mr-6"
            >
              <svg className="w-5 md:w-6 h-5 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Team Cards Container */}
            <div className="flex space-x-4 sm:space-x-6 overflow-hidden flex-1 justify-center">
              {teamMembers.slice(currentIndex, currentIndex + itemsPerView).map((member) => (
                <TeamCard key={member.id} {...member} />
              ))}
            </div>

            {/* Next Button - Hidden on mobile */}
            <button
              onClick={nextTestimonial}
              className="hidden sm:flex w-10 md:w-12 h-10 md:h-12 bg-gray-100 hover:bg-gray-200 rounded-full items-center justify-center text-gray-600 transition-all duration-200 shadow-lg ml-4 md:ml-6"
            >
              <svg className="w-5 md:w-6 h-5 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Mobile Navigation Buttons */}
          <div className="flex sm:hidden justify-center space-x-4 mt-6">
            <button
              onClick={prevTestimonial}
              className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center text-gray-600 transition-all duration-200 shadow-lg"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={nextTestimonial}
              className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center text-gray-600 transition-all duration-200 shadow-lg"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-6 sm:mt-8 md:mt-10 space-x-2">
            {Array.from({ length: Math.ceil(teamMembers.length / itemsPerView) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index * itemsPerView)}
                className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-200 ${
                  Math.floor(currentIndex / itemsPerView) === index ? 'bg-teal-400 w-6 sm:w-8' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}