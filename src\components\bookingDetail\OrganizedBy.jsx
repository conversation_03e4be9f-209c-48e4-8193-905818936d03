"use client";
import React from 'react';
import { Send } from 'lucide-react';
import Image from 'next/image';

const OrganizedBy = ({
  title = "",
  organizerName = "",
  memberSince = "",
  avatar = "",
  onSendMessage
}) => {
  return (
    <div className="bg-white rounded-3xl shadow-lg p-6 text-center">
      <h3 className="text-xl font-bold text-[#0C2C7A] mb-6">{title}</h3>
      
      {/* Avatar */}
      <div className="flex justify-center mb-4">
        <div className="w-20 h-20 rounded-full overflow-hidden">
          <Image 
            src={avatar}
            alt={organizerName}
            width={80}
            height={80}
            className="w-full h-full object-cover"
          />
        </div>
      </div>
      
      {/* Organizer Info */}
      <h4 className="text-lg font-bold text-[#0C2C7A] mb-1">
        {organizerName}
      </h4>
      <p className="text-gray-500 text-sm mb-6">
        {memberSince}
      </p>
      
      {/* Send Message Button */}
      <button 
        onClick={onSendMessage}
        className="w-full bg-[#24BDC7] hover:bg-[#1da8b3] text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <Send size={18} />
        <span>Send Message</span>
      </button>
    </div>
  );
};

export default OrganizedBy;
