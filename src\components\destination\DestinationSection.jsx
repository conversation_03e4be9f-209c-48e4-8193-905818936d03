"use client";
import React, { useState } from 'react';
import SectionBadge from '../ui/SectionBadge';
import DestinationCard from './DestinationCard';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import destinationData from '../../app/data/destinationData.json';

const DestinationSection = () => {
    const [currentSlide, setCurrentSlide] = useState(0);

    const destinations = destinationData;

    const itemsPerPage = {
        mobile: 1,
        tablet: 2,
        desktop: 4
    };

    const maxSlides = {
        mobile: destinations.length - itemsPerPage.mobile,
        tablet: destinations.length - itemsPerPage.tablet,
        desktop: destinations.length - itemsPerPage.desktop
    };

    const nextSlide = () => {
        setCurrentSlide(prev => Math.min(prev + 1, maxSlides.desktop));
    };

    const prevSlide = () => {
        setCurrentSlide(prev => Math.max(prev - 1, 0));
    };

    return (
        <section className="sky-blue-bg bg-[url('/assets/img/shape/01.png')] bg-cover bg-center dark:bg-gray-900 pb-5 p-10">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
                {/* Header */}
                <div className="text-center mb-12 lg:mb-16">
                    <h2 className="text-3xl sm:text-4xl lg:text-4xl xl:text-6xl font-bold text-[#0C2C7A] dark:text-white leading-tight">
                        Where would you like to go?
                    </h2>
                </div>

                {/* Mobile Layout - Single Card with Swipe */}
                <div className="block sm:hidden">
                    <div className="relative">
                        <div className="overflow-hidden">
                            <div 
                                className="flex transition-transform duration-300 ease-in-out"
                                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                            >
                                {destinations.map((destination) => (
                                    <div key={destination.id} className="w-full flex-shrink-0 px-2">
                                        <DestinationCard destination={destination} />
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Navigation Buttons - Mobile */}
                        <button 
                            onClick={prevSlide}
                            disabled={currentSlide === 0}
                            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-[#24BDC7] p-2 rounded-full shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                        >
                            <ChevronLeft size={20} />
                        </button>
                        <button 
                            onClick={nextSlide}
                            disabled={currentSlide >= maxSlides.mobile}
                            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-[#24BDC7] p-2 rounded-full shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                        >
                            <ChevronRight size={20} />
                        </button>
                    </div>

                    {/* Dots Indicator - Mobile */}
                    <div className="flex justify-center mt-6 gap-2">
                        {destinations.map((_, index) => (
                            <button
                                key={index}
                                onClick={() => setCurrentSlide(index)}
                                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                                    index === currentSlide ? 'bg-[#24BDC7] w-6' : 'bg-gray-300'
                                }`}
                            />
                        ))}
                    </div>
                </div>

                {/* Tablet Layout - 2 Cards */}
                <div className="hidden sm:block lg:hidden">
                    <div className="relative">
                        <div className="overflow-hidden">
                            <div 
                                className="flex gap-4 transition-transform duration-300 ease-in-out"
                                style={{ transform: `translateX(-${currentSlide * 50}%)` }}
                            >
                                {destinations.map((destination) => (
                                    <div key={destination.id} className="w-2/5 flex-shrink-0 px-2">
                                        <DestinationCard destination={destination} />
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Navigation Buttons - Tablet */}
                        <button 
                            onClick={prevSlide}
                            disabled={currentSlide === 0}
                            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-[#24BDC7] p-3 rounded-full shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                        >
                            <ChevronLeft size={24} />
                        </button>
                        <button 
                            onClick={nextSlide}
                            disabled={currentSlide >= maxSlides.tablet}
                            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-[#24BDC7] p-3 rounded-full shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                        >
                            <ChevronRight size={24} />
                        </button>
                    </div>
                </div>

                {/* Desktop Layout - 4 Cards with Navigation */}
                <div className="hidden lg:block">
                    <div className="relative">
                        <div className="overflow-hidden">
                            <div 
                                className="flex gap-1 transition-transform duration-300 ease-in-out"
                                style={{ transform: `translateX(-${currentSlide * 25}%)` }}
                            >
                                {destinations.map((destination) => (
                                    <div key={destination.id} className="w-1/4 flex-shrink-0 px-1">
                                        <DestinationCard destination={destination} />
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Navigation Buttons - Desktop */}
                        <button 
                            onClick={prevSlide}
                            disabled={currentSlide === 0}
                            className="absolute -left-6 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-[#24BDC7] p-3 rounded-full shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 z-10"
                        >
                            <ChevronLeft size={24} />
                        </button>
                        <button 
                            onClick={nextSlide}
                            disabled={currentSlide >= maxSlides.desktop}
                            className="absolute -right-6 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-[#24BDC7] p-3 rounded-full shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 z-10"
                        >
                            <ChevronRight size={24} />
                        </button>
                    </div>

                    {/* Dots Indicator - Desktop */}
                    <div className="flex justify-center mt-8 gap-2">
                        {Array.from({ length: maxSlides.desktop + 1 }).map((_, index) => (
                            <button
                                key={index}
                                onClick={() => setCurrentSlide(index)}
                                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                                    index === currentSlide ? 'bg-[#24BDC7] w-8' : 'bg-gray-300'
                                }`}
                            />
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default DestinationSection;
