'use client';
import { useState, useEffect } from 'react';
import TestimonialCard from './testimonialsCard';
import SectionBadge from '../ui/SectionBadge';

function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(2); // Default to 2 for desktop

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      designation: 'Our Clients',
      image: '/assets/img/testimonial/01.jpg',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
    {
      id: 2,
      name: '<PERSON>',
      designation: 'Our Clients',
      image: '/assets/img/testimonial/02.jpg',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
    {
      id: 3,
      name: '<PERSON>',
      designation: 'Our Clients',
      image: '/assets/img/testimonial/03.jpg',
      rating: 5,
      review: 'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
    {
      id: 4,
      name: 'Michael Brown',
      designation: 'Our Clients',
      image: '/assets/img/testimonial/04.jpg',
      rating: 5,
      review:
        'There are many variations passages of available but to the majority have suffered for the alteration in some form injected humour words which look even slig believable.',
    },
  ];

  // Update items per view based on screen size
  useEffect(() => {
    const updateItemsPerView = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth < 640) {
          setItemsPerView(1); // Mobile: 1 item
        } else if (window.innerWidth < 1024) {
          setItemsPerView(1); // Tablet: 1 item
        } else {
          setItemsPerView(2); // Desktop: 2 items
        }
      }
    };

    updateItemsPerView();
    window.addEventListener('resize', updateItemsPerView);
    return () => window.removeEventListener('resize', updateItemsPerView);
  }, []);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex >= testimonials.length - itemsPerView ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex <= 0 ? testimonials.length - itemsPerView : prevIndex - 1
    );
  };

  return (
    <section className=" py-16 lg:pt-5 lg:pb-10">

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <SectionBadge text="TESTIMONIALS" isMobile={false} />
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold my-4 leading-tight">
            What Our Customers Are Saying
            <br />
            About Us?
          </h2>
        </div>

        <div className="relative max-w-6xl mx-auto">
          {/* Navigation Arrows */}
          <div className="flex items-center justify-center">
            <button
              onClick={prevTestimonial}
              className="hidden lg:flex w-12 h-12 bg-white hover:bg-white/20 rounded-full items-center justify-center sky-blue transition-all duration-200 backdrop-blur-sm mr-8"
              aria-label="Previous testimonial"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Testimonials Container */}
            <div className="flex-1 max-w-6xl mb-5">
              <div className="overflow-hidden">
                <div
                  className="flex transition-transform duration-500 ease-in-out lg:gap-1"
                  style={{ transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)` }}
                >
                  {testimonials.map((testimonial) => (
                    <div
                      key={testimonial.id}
                      className="flex-shrink-0 px-2 sm:px-4"
                      style={{ flexBasis: `${100 / itemsPerView}%` }}
                    >
                      <TestimonialCard {...testimonial} />
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <button
              onClick={nextTestimonial}
              className="hidden lg:flex w-12 h-12 bg-white hover:bg-white/20 rounded-full items-center justify-center sky-blue transition-all duration-200 backdrop-blur-sm ml-8"
              aria-label="Next testimonial"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Mobile Navigation Buttons */}
          <div className="flex justify-center gap-4 mt-8 lg:hidden">
            <button
              onClick={prevTestimonial}
              className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-all duration-200 backdrop-blur-sm"
              aria-label="Previous testimonial"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              onClick={nextTestimonial}
              className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-all duration-200 backdrop-blur-sm"
              aria-label="Next testimonial"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Dot Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: Math.ceil(testimonials.length / itemsPerView) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index * itemsPerView)}
                className={`h-2 rounded-full transition-all duration-300 ${
                  Math.floor(currentIndex / itemsPerView) === index
                    ? 'bg-[#25BDC7] w-8'
                    : 'bg-white/40 w-2 hover:bg-white/60'
                }`}
                aria-label={`Go to testimonial slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
       
      </div>
       {/* Background text at bottom */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 opacity-10 pointer-events-none">
          <div className="text-white text-4xl sm:text-6xl lg:text-8xl xl:text-[10rem] font-bold tracking-wider">
            TAVELO
          </div>
        </div>
    </section>
  );
}

export default Testimonials;