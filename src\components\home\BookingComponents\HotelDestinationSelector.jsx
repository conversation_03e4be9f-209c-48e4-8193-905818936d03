// components/home/<USER>/HotelDestinationSelector.jsx
import React, { useState, useRef, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEarthAmericas } from '@fortawesome/free-solid-svg-icons';
import hotelDestinationsData from '../../../app/data/hotelDestinationsData.json';

// Transform hotel destinations data for the component
const HOTEL_DESTINATIONS = hotelDestinationsData.map(destination => ({
  city: destination.city,
  country: destination.country,
  attractions: destination.attractions
}));

const HotelDestinationSelector = ({
  destination,
  onDestinationChange
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  const containerRef = useRef(null);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filterSuggestions = (query) => {
    if (!query || query.length < 2) {
      setFilteredSuggestions([]);
      return;
    }

    const filtered = HOTEL_DESTINATIONS.filter(item => {
      const searchText = `${item.city} ${item.country}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    }).slice(0, 8);

    setFilteredSuggestions(filtered);
  };

  const handleDestinationChange = (e) => {
    const value = e.target.value;
    if (onDestinationChange) {
      onDestinationChange({
        ...destination,
        city: value
      });
    }
    filterSuggestions(value);
    setShowSuggestions(true);
  };

  const handleSuggestionClick = (suggestion) => {
    if (onDestinationChange) {
      onDestinationChange({
        city: suggestion.city,
        country: suggestion.country,
        attractions: suggestion.attractions
      });
    }
    setShowSuggestions(false);
    setFilteredSuggestions([]);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    // Show suggestions if there's already text
    if (destination.city) {
      filterSuggestions(destination.city);
      setShowSuggestions(true);
    }
  };

  return (
    <div className="col-span-12 lg:col-span-3 text-[#0C2C7A]" ref={containerRef}>
      <div className={`relative pb-3 bg-[#E4F7F8] rounded-lg p-1 border-2 transition-colors min-h-[4rem] flex flex-col justify-between ${
        isFocused ? 'border-[#24BDC7]' : 'border-[#E4F7F8]'
      }`}>
        <div className="flex items-center justify-between">
          <label className="block text-xs font-medium mb-0.5">Destination</label>
          <FontAwesomeIcon icon={faEarthAmericas} className="text-[#24BDC7] text-sm" />
        </div>

        {/* Destination Input */}
        <div className="relative">
          <input
            type="text"
            value={destination.city || ''}
            onChange={handleDestinationChange}
            onFocus={handleInputFocus}
            onBlur={() => setTimeout(() => setIsFocused(false), 150)}
            placeholder="Where do you want to stay?"
            className="w-full bg-transparent text-sm lg:text-base font-semibold text-[#0C2C7A] placeholder-gray-400 outline-none rounded p-1 transition-colors"
          />
        </div>

        {/* Suggestions Dropdown */}
        {showSuggestions && filteredSuggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-48 lg:max-h-64 overflow-y-auto">
            {filteredSuggestions.map((suggestion, index) => (
              <div
                key={`${suggestion.city}-${suggestion.country}-${index}`}
                onClick={() => handleSuggestionClick(suggestion)}
                className="px-3 lg:px-4 py-2 lg:py-3 hover:bg-[#E4F7F8] cursor-pointer border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="text-base lg:text-lg font-semibold text-[#0C2C7A] truncate">{suggestion.city}</div>
                  </div>
                  <div className="text-sm text-[#24BDC7] ml-2 flex-shrink-0">
                    <svg className="w-4 h-4 lg:w-5 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default HotelDestinationSelector;
