"use client";
import BlogCard from "@/components/blog/blogCard";
import blogs from "../data/blogs.json";
import About from "@/components/about/About";
import TestimonialsSection from "@/components/testimonials/testimonialsSection";
import FeaturesSection from "@/components/features/FeaturesSection";
import TeamSection from "@/components/teams/teamSection";
import AppDownloadSection from "@/components/home/<USER>";
import CarRentalSection from "@/components/carRental/carRentalSection";
import DestinationSection from "@/components/destination/DestinationSection";
import OurVideo from "@/components/ourvideo/OurVideo";
import OffersSection from "@/components/offers/OffersSection";
import SectionBadge from '@/components/ui/SectionBadge';
import HotelSection from "@/components/hotel/hotelSection";
import FlightSection from "@/components/flight/flightSection";
import TourSection from "@/components/tour/tourSection";
import WhyChooseUs from "@/components/chooseUs/WhyChooseUs";
import Header from "@/components/home/<USER>";
import Booking from "@/components/home/<USER>";
import Testimonials from "@/components/testimonials/testimonials";
import StatsSection from "@/components/ui/StatsSection";



export default function HomePage() {
  return (
    <>
      <Header
        backgroundImage="/assets/img/hero/hero-1.jpg"
        height="lg:min-h-[100vh] [@media(min-width:1440px)]:min-h-[60vh]"
        heroTitle="Explore The World Together"
        heroSubtitle="Find Awesome Flight, Hotel, Tour, Car And Packages"
      />
    {/* Booking Component */}
      <div className="relative lg:left-24 lg:bottom-20  z-10 -mt-16 lg:-mt-32  w-full lg:w-[85%] ">
        <Booking />
      </div>
      <FlightSection />
      <DestinationSection />
      <HotelSection />
      <TourSection />
      <About />
      <StatsSection />
      {/* <FeaturesSection /> */}
      <OurVideo />
      {/* <OffersSection /> */}
      <div className="m-10">
      <TestimonialsSection />
</div>
      {/* <WhyChooseUs />
      <CarRentalSection />
      <TeamSection />
      <AppDownloadSection /> */}


      {/* Latest Blogs */}
      {/* <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10 sm:py-16 md:py-20">
        <div className="site-heading text-center mb-8 sm:mb-12 md:mb-16">
          <SectionBadge text="OUR BLOG" isMobile={false} />

          <h2 className="site-title text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-4">
            Our Latest Blog & News
          </h2>
        </div>

        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 justify-items-center">
          {blogs.map((blog) => (
            <BlogCard key={blog.id} blog={blog} />
          ))}
        </div> 
      </div> */}
    </>
  );
}
