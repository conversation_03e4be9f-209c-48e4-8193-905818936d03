"use client";
import React from "react";
import Image from "next/image";
import FeatureCard from "../features/FeatureCard";
import SectionBadge from "../ui/SectionBadge";
import ActionButton from "../ui/ActionButton";

const FeaturesSection = () => {
  const features = [
    {
      id: 1,
      icon: "/assets/img/icon/world.svg",
      title: "Worldwide Coverage",
      description:
        "It is a long established fact that a reader will of page when looking at its layout.",
    },
    {
      id: 2,
      icon: "/assets/img/icon/quality.svg",
      title: "Best Quality Services",
      description:
        "It is a long established fact that a reader will of page when looking at its layout.",
    },
    {
      id: 3,
      icon: "/assets/img/icon/support.svg",
      title: "24/7 Customer Service",
      description:
        "It is a long established fact that a reader will of page when looking at its layout.",
    },
  ];

  return (
    <section className="bg-gray-50 dark:bg-gray-900 py-12 sm:py-16 lg:py-20 xl:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        {/* Mobile Layout - Stack everything vertically */}
        <div className="block lg:hidden">
          {/* Header Section - Mobile */}
          <div className="text-left mb-8 sm:mb-12">
            <SectionBadge text="FEATURES" isMobile={true} />
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#0C2C7A] dark:text-white mb-4 sm:mb-6 leading-tight">
              Let's Check Our
              <span className="text-[#24BDC7] block">Awesome Features</span>
            </h2>
            <p className="text-[#757F95] dark:text-gray-300 text-base sm:text-lg leading-relaxed mb-6 sm:mb-8 max-w-2xl mx-auto">
              There are many variations of passages orem ipsum available but
              the majority have suffered alteration in some form, by if you
              are going to use a passage orem ipsum you need to be sure there
              isn't anything embarrassing hidden injected humour, or
              randomised words which don't look even slightly believable.
            </p>
          </div>

          {/* Image Section - Mobile */}
          <div className="flex justify-center mb-8 sm:mb-12">
            <div className="relative">
              <div className="w-64 h-64 sm:w-80 sm:h-80 md:w-96 md:h-96 mx-auto relative">
                <div className="relative w-full h-full rounded-[3rem] sm:rounded-[4rem] md:rounded-[5rem] overflow-hidden shadow-2xl">
                  <Image
                    src="/assets/img/feature/01.jpg"
                    alt="Travel Adventure"
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 256px, (max-width: 768px) 320px, 384px"
                  />
                </div>
              </div>
            </div>
          </div>

        {/* Feature Cards - Mobile */}
                  <div className="grid grid-cols-1 gap-4 sm:gap-6">
                    {features.map((feature, index) => (
                      <FeatureCard 
                        key={feature.id}
                        feature={feature}
                        index={index}
                        isMobile={true}
                      />
                    ))}
                  </div>

        {/* Learn More Button - Mobile */}
          <div className="text-left mt-8 sm:mt-12">
            <ActionButton text="Learn More" isMobile={true} />
          </div>
        </div>

        {/* Desktop Layout - 3 Column Grid */}
        <div className="hidden lg:grid lg:grid-cols-3 gap-8 xl:gap-12 items-center">
          {/* Left Side - Header & Content */}
          <div className="order-1">
            <div className="mb-8 lg:mb-12">
              <SectionBadge text="FEATURES" isMobile={false} />
              <h2 className="text-4xl xl:text-5xl font-bold text-[#0C2C7A] dark:text-white mb-6 leading-tight">
                Let's Check Our
                <span className="text-[#24BDC7] block">Awesome Features</span>
              </h2>

              <p className="text-[#757F95] dark:text-gray-300 text-lg leading-relaxed mb-8">
                There are many variations of passages orem ipsum available but
                the majority have suffered alteration in some form, by if you
                are going to use a passage orem ipsum you need to be sure there
                isn't anything embarrassing hidden injected humour, or
                randomised words which don't look even slightly believable.
              </p>

              <ActionButton text="Learn More" isMobile={false} />
            </div>
          </div>

          {/* Middle - Image */}
          <div className="order-2">
            <div className="relative">
              <div className="w-80 h-80 xl:w-96 xl:h-96 mx-auto relative">
                <div className="relative w-full h-full rounded-[6rem] xl:rounded-[7rem] overflow-hidden shadow-2xl">
                  <Image
                    src="/assets/img/feature/01.jpg"
                    alt="Travel Adventure"
                    fill
                    className="object-cover"
                    sizes="(max-width: 1280px) 320px, 384px"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Feature Cards */}
          <div className="order-3">
            <div className="space-y-6">
              {features.map((feature, index) => (
                <FeatureCard 
                  key={feature.id}
                  feature={feature}
                  index={index}
                  isMobile={false}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
