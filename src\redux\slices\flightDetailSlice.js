// src/redux/slices/flightDetailSlice.js
import { fetchFlightDetails } from "@/api/flightDetailApi";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

// Async thunk for fetching flight details
export const fetchFlightDetail = createAsyncThunk(
  "flightDetail/fetchFlightDetail",
  async (fareId, { rejectWithValue }) => {
    // Use the fareId parameter instead of hardcoded value
    const selectedFareId = fareId || "fare-0099"; // Fallback to default if no fareId provided
    try {
      const res = await fetchFlightDetails(selectedFareId);
      console.log("Fetched flight detail for fareId:", selectedFareId, res.data); // Debugging response
      return res.data;
    } catch (err) {
      return rejectWithValue(
        err.response?.data?.message || "Failed to fetch flight details"
      );
    }
  }
);

const flightDetailSlice = createSlice({
  name: "flightDetail",
  initialState: {
    detail: null, // flight detail object
    loading: false,
    error: null,
    currentFareId: null, // Store the current fareId being viewed
  },
  reducers: {
    clearFlightDetail: (state) => {
      state.detail = null;
      state.error = null;
      state.loading = false;
      state.currentFareId = null;
    },
    setCurrentFareId: (state, action) => {
      state.currentFareId = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFlightDetail.pending, (state, action) => {
        state.loading = true;
        state.error = null;
        // Store the fareId being fetched
        state.currentFareId = action.meta.arg;
      })
      .addCase(fetchFlightDetail.fulfilled, (state, action) => {
        state.loading = false;
        console.log("Action payload (flightDetail):", action.payload);
        state.detail = action.payload;
      })
      .addCase(fetchFlightDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.currentFareId = null;
      });
  },
});

export const { clearFlightDetail, setCurrentFareId } = flightDetailSlice.actions;
export default flightDetailSlice.reducer;
