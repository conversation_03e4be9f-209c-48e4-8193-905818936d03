import { useState, useEffect } from 'react';
import HotelCard from './hotelCard';
import hotelsData from '../../app/data/hotelData.json';
import SectionBadge from '../ui/SectionBadge';
import { Bed } from 'lucide-react';

export default function HotelBookingSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(4);
  const hotels = hotelsData;

  // Update items per view based on screen size
  useEffect(() => {
    const updateItemsPerView = () => {
      if (typeof window !== 'undefined') {
        if (window.innerWidth < 640) {
          setItemsPerView(1); // Mobile: 1 item
        } else if (window.innerWidth < 1024) {
          setItemsPerView(2); // Tablet: 2 items
        } else {
          setItemsPerView(4); // Desktop: 3 items
        }
      }
    };

    updateItemsPerView();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updateItemsPerView);
      return () => window.removeEventListener('resize', updateItemsPerView);
    }
  }, []);

  const nextHotel = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex >= hotels.length - itemsPerView ? 0 : prevIndex + 1
    );
  };

  const prevHotel = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex <= 0 ? hotels.length - itemsPerView : prevIndex - 1
    );
  };

  const goToSlide = (index) => {
    setCurrentIndex(index * itemsPerView);
  };

  return (
    <section className="bg-white py-5 sm:py-16 md:py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 md:mb-16">
          <SectionBadge text="HOTEL" isMobile={false} />
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#0C2C7A] my-4">
            Our Most Popular Hotels
          </h2>
        </div>

        {/* Hotel Carousel */}
        <div className="relative max-w-7xl mx-auto">
          {/* Desktop Layout */}
          <div className="hidden sm:flex justify-center items-center">
            {/* Previous Button */}
            <button
              onClick={prevHotel}
              className="flex w-10 md:w-12 h-10 md:h-12 bg-white hover:bg-gray-50 rounded-full items-center justify-center text-gray-600 transition-all duration-200 shadow-lg mr-4 md:mr-6 z-10"
            >
              <svg className="w-5 md:w-6 h-5 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Hotel Cards Container */}
            <div className="flex space-x-6 gap-4 overflow-hidden justify-center">
              {hotels.slice(currentIndex, currentIndex + itemsPerView).map((hotel) => (
                <HotelCard key={hotel.id} {...hotel} />
              ))}
            </div>

            {/* Next Button */}
            <button
              onClick={nextHotel}
              className="flex w-10 md:w-12 h-10 md:h-12 bg-white hover:bg-gray-50 rounded-full items-center justify-center text-gray-600 transition-all duration-200 shadow-lg ml-4 md:ml-6 z-10"
            >
              <svg className="w-5 md:w-6 h-5 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Mobile Layout */}
          <div className="sm:hidden">
            <div className="px-4">
              {hotels.slice(currentIndex, currentIndex + itemsPerView).map((hotel) => (
                <HotelCard key={hotel.id} {...hotel} />
              ))}
            </div>
          </div>

          {/* Mobile Navigation Buttons */}
          <div className="flex sm:hidden justify-center space-x-4 mt-6">
            <button
              onClick={prevHotel}
              className="w-10 h-10 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center text-gray-600 transition-all duration-200 shadow-lg"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={nextHotel}
              className="w-10 h-10 bg-white hover:bg-gray-50 rounded-full flex items-center justify-center text-gray-600 transition-all duration-200 shadow-lg"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-6 sm:mt-8 space-x-2">
            {Array.from({ length: Math.ceil(hotels.length / itemsPerView) }).map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-200 ${
                  Math.floor(currentIndex / itemsPerView) === index ? 'bg-[#24BDC7] w-6 sm:w-8' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}