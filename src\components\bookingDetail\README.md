# Flight Detail Page Component

This component displays detailed flight information with a clean, organized layout. It has been refactored to eliminate duplicate data and properly integrate with API responses.

## Features

- ✅ **Single Source of Truth**: All default data centralized in one location
- ✅ **API Integration**: Seamlessly maps API responses to component format
- ✅ **Responsive Design**: Works on all device sizes
- ✅ **Clean Architecture**: Proper parent-to-child data flow
- ✅ **Error Handling**: Built-in loading and error states

## Usage

### Basic Usage with API Data

```jsx
import FlightDetailPage from '@/components/bookingDetail/FlightDetailPage';

const MyPage = () => {
  const [flightData, setFlightData] = useState(null);

  // Your API call here
  useEffect(() => {
    fetchFlightData().then(setFlightData);
  }, []);

  return (
    <FlightDetailPage 
      apiFlightData={flightData}
      className="bg-gray-50"
    />
  );
};
```

### Using with Next.js Dynamic Routes

```jsx
// pages/flight-detail/[id].jsx
import { useRouter } from 'next/router';
import { useFlightDetail } from '@/hooks/useFlightDetail';
import FlightDetailPage from '@/components/bookingDetail/FlightDetailPage';

export default function FlightDetail() {
  const router = useRouter();
  const { id } = router.query;
  
  const { data, loading, error } = useFlightDetail(id);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return <FlightDetailPage apiFlightData={data} />;
}
```

## API Data Format

The component expects API data in this format:

```json
{
  "id": "fare-0001",
  "currency": "USD",
  "segments": [
    {
      "flightNumber": "LH7399",
      "airlineCode": "LH",
      "airlineDisplayName": "Lufthansa",
      "airlineRating": 4.2,
      "reviews": 1150,
      "passengers": {
        "adults": 5,
        "children": 4
      },
      "images": [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg"
      ],
      "duration": "5h 39m",
      "departure": {
        "city": "New York",
        "cityCode": "JFK",
        "scheduledDeparture": "2025-12-18T00:00:00.000Z"
      },
      "arrival": {
        "city": "Reykjavik",
        "cityCode": "KEF",
        "scheduledArrival": "2025-12-18T05:39:00.000Z"
      }
    }
  ],
  "serviceClass": "FIRST",
  "baggageAllowance": "Cabin: 10kg, Check-in: 30kg",
  "refundable": "Partially Refundable",
  "price": 3183,
  "totalPrice": 3157.54,
  "flightType": "oneWay"
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `apiFlightData` | Object | `null` | Raw API response data |
| `flightData` | Object | `{}` | Override flight display data |
| `aboutData` | Object | `{}` | Override about section data |
| `inflightData` | Object | `{}` | Override inflight features data |
| `fareRulesData` | Object | `{}` | Override fare rules data |
| `sidebarData` | Object | `{}` | Override sidebar data |
| `className` | String | `""` | Additional CSS classes |

## File Structure

```
src/components/bookingDetail/
├── FlightDetailPage.jsx          # Main component
├── FlightCard.jsx                # Flight information card
├── AboutSection.jsx              # Airline about section
├── InflightFeatures.jsx          # Inflight amenities
├── BaggageSection.jsx            # Baggage information
├── FareRules.jsx                 # Fare rules and policies
├── FAQ.jsx                       # Frequently asked questions
├── Reviews.jsx                   # Customer reviews
├── ReviewForm.jsx                # Review submission form
├── DetailSidebar.jsx             # Sidebar container
├── BookingWidget.jsx             # Booking price and actions
├── WhyBookWithUs.jsx             # Benefits section
├── ContactCard.jsx               # Contact information
├── OrganizedBy.jsx               # Organizer information
└── README.md                     # This file

src/hooks/
└── useFlightDetail.js            # Custom hook for API calls

src/app/
├── flight-detail/[id]/page.jsx   # Next.js page component
└── api/flight-detail/[id]/route.js # API route example
```

## Data Mapping

The component automatically maps API data to the internal format:

- `segments[0].departure/arrival` → Flight times and locations
- `segments[0].airlineDisplayName` → Airline information
- `segments[0].images` → Flight images
- `price/totalPrice` → Pricing information
- `serviceClass` → Flight class
- `baggageAllowance` → Baggage information

## Customization

You can override any default data by passing props:

```jsx
<FlightDetailPage 
  apiFlightData={apiData}
  aboutData={{
    title: "Custom Airline Info",
    content: ["Custom content here"]
  }}
  sidebarData={{
    contactData: {
      phone: "******-0123",
      email: "<EMAIL>"
    }
  }}
/>
```

## Testing

Test URLs (when using the example API route):
- `/flight-detail/fare-0001` - Normal flight data
- `/flight-detail/not-found` - 404 error
- `/flight-detail/error` - 500 error
