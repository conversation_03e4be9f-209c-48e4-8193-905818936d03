import React from 'react';

const FeatureCard = ({ icon, title, description, offset = 0 }) => {
    return (
        <div className={`bg-white rounded-l-none rounded-r-full rounded-tl-full px-2 py-6 sm:p-1 md:p-1 lg:p-4 xl:p-2 relative transition-all duration-300 hover:shadow-xl h-14 sm:h-16 md:h-20 lg:h-20 xl:h-24 flex items-center || ''}`} style={{ boxShadow: '0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.1)' }}>
            <div className="flex items-center gap-2 sm:gap-2 md:gap-3 lg:gap-4 w-full px-4">
                {/* Icon */}
                <div className="flex-shrink-0">
                    <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-10 md:h-10 lg:w-12 lg:h-12 xl:w-14 xl:h-14 bg-[#24BDC7] rounded-full flex items-center justify-center shadow-lg">
                        <img
                            src={`/assets/img/icon/${icon}`}
                            alt={`${title} icon`}
                            className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-5 lg:h-5 xl:w-6 xl:h-6 filter brightness-0 invert"
                        />
                    </div>
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                    <h3 className="text-[8px] sm:text-[9px] md:text-xs lg:text-xs xl:text-sm font-semibold text-[#0C2C7A] mb-1 leading-tight">
                        {title}
                    </h3>
                    <p className="text-[#757F95] text-[6px] sm:text-[7px] md:text-[8px] lg:text-[9px] xl:text-xs pb-1" style={{ display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical' }}>
                        {description}
                    </p>
                </div>
            </div>
        </div>
    );
};

export default FeatureCard;
