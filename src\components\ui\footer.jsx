'use client';
import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { FaHeadset, FaMapMarkerAlt, FaEnvelope, FaLock, FaPaperPlane } from 'react-icons/fa';
import { FaFacebookF, FaTwitter, FaLinkedinIn, FaYoutube } from 'react-icons/fa';
import LogoSlider from './footerSlider';

const Footer = () => {
  const [email, setEmail] = useState('');
  const t = useTranslations();

  const handleNewsletterSubmit = (e) => {
    e.preventDefault();
    // Handle newsletter subscription
    console.log('Newsletter subscription:', email);
    setEmail('');
  };

  return (
    <footer className="relative bg-[url('/assets/img/shape/02.png')] mt-5 bg-cover px-10 bg-center bg-no-repeat text-white relative before:absolute before:inset-0 before:bg-gradient-to-r before:from-blue-900 before:to-blue-800 before:opacity-80">
<div className="absolute top-[-3.5rem] w-full h-[10rem] left-0 lg:w-[60%] overflow-hidden">
  <LogoSlider />
</div>
      {/* Main Footer Content */}
      
    
      <div className="pt-16 pb-8 relative z-10">
        <div className="container mx-auto px-4 text-center">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mt-5">
            {/* Company Info */}
            <div className="lg:col-span-1 text-start">
              <Link href="/" className="inline-block mb-4">
                <img
                  src="/assets/img/logo/logo.png"
                  alt="Tavelo"
                  width={120}
                  height={40}
                  className="h-10 w-auto"
                />
              </Link>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Welcome to Tavelo, your gateway to unforgettable travel experiences worldwide.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-600 p-2 rounded-lg">
                    <FaHeadset className="text-white" />
                  </div>
                  <div>
                    <h6 className="font-semibold text-white mb-1">24/7 Call Service</h6>
                    <Link href="tel:+1234567890" className="text-gray-300 hover:text-blue-300">
                      ****** 567 890
                    </Link>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 text-gray-300">
                  <FaMapMarkerAlt className="text-blue-600" />
                  <span>123 Travel Lane, New York, NY</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <FaEnvelope className="text-blue-600" />
                  <Link href="mailto:<EMAIL>" className="text-gray-300 hover:text-blue-300">
                    <EMAIL>
                  </Link>
                </div>
              </div>
            </div>

            {/* Our Company */}
            <div>
              <h4 className="text-xl font-semibold text-white mb-6 text-start">Our Company</h4>
              <ul className="space-y-3">
                <li>
                  <Link href="/about" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/team" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Meet Our Team
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/affiliate" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Affiliate Program
                  </Link>
                </li>
                <li>
                  <Link href="/advertising" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Advertising With Us
                  </Link>
                </li>
                <li>
                  <Link href="/careers" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Careers
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Our Blog
                  </Link>
                </li>
              </ul>
            </div>

            {/* Other Services */}
            <div>
              <h4 className="text-xl font-semibold text-white mb-6 text-start">Other Services</h4>
              <ul className="space-y-3">
                <li>
                  <Link href="/rewards" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Rewards Program
                  </Link>
                </li>
                <li>
                  <Link href="/partners" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Partners
                  </Link>
                </li>
                <li>
                  <Link href="/community" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Community Program
                  </Link>
                </li>
                <li>
                  <Link href="/investors" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Investor Relations
                  </Link>
                </li>
                <li>
                  <Link href="/developer" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Developer Guide
                  </Link>
                </li>
                <li>
                  <Link href="/api" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Travel API
                  </Link>
                </li>
                <li>
                  <Link href="/points" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    PointsPLUS
                  </Link>
                </li>
              </ul>
            </div>

            {/* Help Center & Newsletter */}
            <div>
              <h4 className="text-xl font-semibold text-white mb-6 text-start">Help Center</h4>
              <ul className="space-y-3 mb-8">
                <li>
                  <Link href="/account" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Account
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    FAQs
                  </Link>
                </li>
                <li>
                  <Link href="/legal" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Legal Notice
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="flex items-center text-gray-300 hover:text-blue-300 transition-colors">
                    Terms & Conditions
                  </Link>
                </li>
              </ul>

            </div>
             {/* Newsletter */}
              <div className='text-start'>
                <h5 className="text-lg font-semibold text-white mb-4">Newsletter</h5>
                <p className="text-gray-300 mb-4">Subscribe to get the latest updates and travel offers!</p>
                <form onSubmit={handleNewsletterSubmit} className="space-y-4">
                  <div className="relative">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Your Email"
                      className="w-full px-4 py-3 pr-12 bg-white/10 border border-blue-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                      required
                    />
                    <FaEnvelope className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-white text-blue-800 font-bold py-3 px-6 rounded-lg hover:bg-blue-100 transition"
                  >
                    Subscribe Now <FaPaperPlane className="ml-2 inline" />
                  </button>
                  <p className="text-sm text-gray-400 flex items-center justify-center">
                    <FaLock className="mr-2" /> Your information is safe with us.
                  </p>
                </form>

                {/* Payment Methods */}
                <div className="mt-8">
                  <h6 className="text-white font-semibold mb-4">We Accept:</h6>
                  <div className="flex flex-wrap gap-3 justify-center">
                    <Image src="/assets/img/payment/paypal.svg" alt="PayPal" width={40} height={25} />
                    <Image src="/assets/img/payment/mastercard.svg" alt="Mastercard" width={40} height={25} />
                    <Image src="/assets/img/payment/visa.svg" alt="Visa" width={40} height={25} />
                    <Image src="/assets/img/payment/discover.svg" alt="Discover" width={40} height={25} />
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>

      

      {/* Copyright */}
      <div className="border-t border-dashed border-blue-700 py-4 relative z-10">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <p className="text-gray-400 text-sm mb-4 md:mb-0">
              © Copyright {new Date().getFullYear()} <Link href="/" className="text-blue-300 hover:text-blue-200">Tavelo</Link> All Rights Reserved.
            </p>
            
            {/* Social Links */}
            <div className="flex space-x-4">
              <Link href="#" className="w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-gray-300 hover:bg-blue-600 hover:text-white transition-colors">
                <FaFacebookF />
              </Link>
              <Link href="#" className="w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-gray-300 hover:bg-blue-600 hover:text-white transition-colors">
                <FaTwitter />
              </Link>
              <Link href="#" className="w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-gray-300 hover:bg-blue-600 hover:text-white transition-colors">
                <FaLinkedinIn />
              </Link>
              <Link href="#" className="w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-gray-300 hover:bg-blue-600 hover:text-white transition-colors">
                <FaYoutube />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;