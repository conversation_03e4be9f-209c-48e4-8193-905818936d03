"use client";
import React from 'react';
import { Shield, Headphones, MapPin, Plane, Car, CheckCircle } from 'lucide-react';

const WhyBookWithUs = ({
  title = "Why Book With Us?",
  features = []
}) => {

  return (
    <div className="bg-white rounded-3xl shadow-lg p-6">
      <h3 className="text-xl font-bold text-[#0C2C7A] mb-6">{title}</h3>
      
      <div className="space-y-4">
        {features.map((feature, index) => {
          const IconComponent = feature.icon;
          
          return (
            <div key={index} className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-[#24BDC7] rounded-full rounded-bl-none flex items-center justify-center flex-shrink-0 mt-1">
                <IconComponent size={16} className="text-white" />
              </div>
              <div className="flex-1 items-center">
                <h4 className="font-semibold text-[#0C2C7A] text-sm mb-1">
                  {feature.title}
                </h4>
                {/* {feature.description && (
                  <p className="text-gray-600 text-xs leading-relaxed">
                    {feature.description}
                  </p>
                )} */}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default WhyBookWithUs;
