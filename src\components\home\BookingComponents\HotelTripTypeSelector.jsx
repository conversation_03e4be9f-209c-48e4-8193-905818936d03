// components/home/<USER>/HotelTripTypeSelector.jsx
import React from 'react';

const HotelTripTypeSelector = ({ hotelTripType, onChange }) => {
  const hotelTripTypes = [
    { value: 'single', label: 'Single ' },
    { value: 'multiCity', label: 'Multi City ' }
  ];

  return (
    <div className="flex gap-1 mb-1">
      {hotelTripTypes.map((type) => (
        <label key={type.value} className="flex items-center cursor-pointer">
          <input
            type="radio"
            name="hotelTrip"
            value={type.value}
            checked={hotelTripType === type.value}
            onChange={(e) => onChange(e.target.value)}
            className="w-1.5 h-1.5 lg:w-2 lg:h-2 text-[#24BDC7] mr-1"
          />
          <span className="text-[#0C2C7A] text-xs font-bold lg:text-xs">{type.label}</span>
        </label>
      ))}
    </div>
  );
};

export default HotelTripTypeSelector;
