"use client";
import React, { useState, useEffect } from 'react';
import FlightDetailPage from './FlightDetailPage';

const FlightDetailPageExample = () => {
  const [flightData, setFlightData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Example API data (replace with your actual API call)
  const sampleApiData = {
    "id": "fare-0001",
    "currency": "USD",
    "segments": [
        {
            "flightNumber": "LH7399",
            "airlineCode": "LH",
            "airlineDisplayName": "Lufthansa",
            "airlineLogo": "https://example.com/logos/lufthansa.png",
            "airlineRating": 4.2,
            "reviews": 1150,
            "passengers": {
                "adults": 5,
                "children": 4
            },
            "images": [
                "https://live.themewild.com/tavelo/assets/img/flight/single-1.jpg",
                "https://live.themewild.com/tavelo/assets/img/flight/single-2.jpg",
                "https://live.themewild.com/tavelo/assets/img/flight/single-3.jpg"
            ],
            "duration": "5h 39m",
            "departure": {
                "city": "New York",
                "cityCode": "JFK",
                "countryCode": "US",
                "countryName": "United States",
                "scheduledDeparture": "2025-12-18T00:00:00.000Z"
            },
            "arrival": {
                "city": "Reykjavik",
                "cityCode": "KEF",
                "countryCode": "IS",
                "countryName": "United States",
                "scheduledArrival": "2025-11-14T16:16:00.766Z"
            }
        }
    ],
    "serviceClass": "FIRST",
    "baggageAllowance": "Cabin: 10kg, Check-in: 30kg",
    "refundable": "Partially Refundable",
    "price": 3183,
    "discountRate": 0.14,
    "tax": 420.16,
    "totalPrice": 3157.54,
    "flightType": "oneWay",
    "legs": [
        {
            "from": "JFK",
            "to": "KEF",
            "date": "2025-12-18"
        }
    ],
    "status": "available"
  };

  // Simulate API call
  useEffect(() => {
    const fetchFlightData = async () => {
      try {
        setLoading(true);
        // Replace this with your actual API call
        // const response = await fetch('/api/flight-detail/fare-0001');
        // const data = await response.json();
        
        // For now, using sample data
        setTimeout(() => {
          setFlightData(sampleApiData);
          setLoading(false);
        }, 1000);
        
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchFlightData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#24BDC7]"></div>
          <p className="mt-4 text-gray-600">Loading flight details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">Error loading flight details: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <FlightDetailPage 
      apiFlightData={flightData}
      className="bg-gray-50"
    />
  );
};

export default FlightDetailPageExample;
