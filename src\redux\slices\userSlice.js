// /redux/slices/userSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiRequest } from '@/utils/api';

export const fetchUser = createAsyncThunk(
  'user/fetchUser',
  async (url, thunkAPI) => {
    try {
      return await apiRequest({ url });
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Check if user is logged in from cookie
const checkAuthStatus = () => {
  if (typeof window !== 'undefined') {
    const cookies = document.cookie.split(';');
    const userCookie = cookies.find(cookie => cookie.trim().startsWith('user_logged_in='));
    return userCookie ? userCookie.split('=')[1] === 'true' : false;
  }
  return false;
};

const userSlice = createSlice({
  name: 'user',
  initialState: {
    data: null,
    loading: false,
    error: null,
    isAuthenticated: checkAuthStatus(),
    user: null, // Store user info like name, email, etc.
  },
  reducers: {
    loginSuccess: (state, action) => {
      state.isAuthenticated = true;
      state.user = action.payload;
      state.error = null;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.data = null;
      // Clear cookie
      if (typeof window !== 'undefined') {
        document.cookie = 'user_logged_in=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      }
    },
    clearError: (state) => {
      state.error = null;
    },
    updateUser: (state, action) => {
      state.user = { ...state.user, ...action.payload };
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUser.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { loginSuccess, logout, clearError, updateUser } = userSlice.actions;
export default userSlice.reducer;
