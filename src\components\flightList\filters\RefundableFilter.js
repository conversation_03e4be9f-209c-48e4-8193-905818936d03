import React from 'react';
import FilterSection from './FilterSection';
import CheckboxItem from './CheckboxItem';

const RefundableFilter = ({ filters, onFilterChange }) => {
  const refundOptions = [
    { label: 'Non Refundable', count: '20' },
    { label: 'Refundable', count: '15' },
    { label: 'As Per Rules', count: '18' }
  ];

  return (
    <FilterSection title="Refundable">
      {refundOptions.map((option) => (
        <CheckboxItem
          key={option.label}
          label={option.label}
          count={option.count}
          checked={filters.refundable?.includes(option.label)}
          onChange={(e) => onFilterChange('refundable', option.label, e.target.checked)}
        />
      ))}
    </FilterSection>
  );
};

export default RefundableFilter;
