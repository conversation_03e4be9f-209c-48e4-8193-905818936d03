import { useState } from 'react';

const TourCard = ({
  flightName,
  departure,
  arrival,
  rating,
  reviews,
  duration,
  features,
  price,
  image,
  badge
}) => {
  const [isLiked, setIsLiked] = useState(false);

  const renderStars = (rating) => {
    const fullStars = Math.floor(rating);

    return [...Array(5)].map((_, index) => (
      <svg
        key={index}
        className={`w-3 h-3 sm:w-3 sm:h-3 ${index < fullStars ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <div className="bg-white rounded-tl-3xl rounded-tr-3xl rounded-br-4xl p-1 mx-auto sm:p-2 rounded-bl-none overflow-hidden w-full max-w-[20rem] sm:max-w-[16rem] md:max-w-[15rem] shadow-xl hover:shadow-xl transition-shadow duration-300 relative">

      {/* Badge */}
      {badge && (
        <div className="absolute z-10 top-[-40%] sm:top-0 right-1 sm:right-2 bg-[#24BDC7] text-white px-1 sm:px-2 py-0.5 sm:py-1 rounded-t-full rounded-br-full text-xs font-semibold">
          {badge}
        </div>
      )}

      {/* Image Section */}
      <div className="relative h-28 sm:h-28 md:h-30">
        <img
          src={image}
          alt={flightName}
          className="w-full h-full object-cover rounded-t-2xl rounded-br-4xl"
        />

        {/* Heart Icon */}
        <div
          onClick={() => setIsLiked(!isLiked)}
          className="absolute border-2 border-white top-0 sm:top-0 left-0 sm:left-0 w-5 sm:w-6 h-5 sm:h-6 bg-[#24BDC7] rounded-full flex items-center justify-center"
        >
          <svg
            className={`w-4 sm:w-5 h-4 sm:h-5 text-white ${isLiked ? 'fill-red-500' : 'fill-none stroke-white'}`}
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </div>
      </div>

      {/* Content Section */}
      <div className="p-1 sm:p-2 md:p-3">
        {/* Flight Name */}
        <h3 className="text-xs sm:text-sm md:text-base font-bold text-gray-800 mb-1 sm:mb-1 line-clamp-1">{flightName}</h3>

        {/* Route */}
        <div className="flex items-center text-gray-600 text-xs sm:text-sm mb-1 sm:mb-2">
          <svg className="w-2 h-2 sm:w-3 sm:h-3 mr-1 text-gray-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
          </svg>
          <span className="truncate">{departure} - {arrival}</span>
        </div>

        {/* Flight Details */}
        <div className="flex items-center justify-between text-xs sm:text-sm text-gray-600 mb-1 sm:mb-2">
          <div className="flex items-center">
            <svg className="w-2 h-2 sm:w-3 sm:h-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 4a2 2 0 00-2 2v1h10V6a2 2 0 00-2-2H5zm-2 5v6a2 2 0 002 2h10a2 2 0 002-2V9H3zm2-1a1 1 0 00-1 1v6a1 1 0 001 1h6a1 1 0 001-1V9a1 1 0 00-1-1H5z" />
            </svg>
            <span>{duration}</span>
          </div>
        </div>

        {/* Price and Action */}
        <div className="flex items-center justify-between pt-1 sm:pt-2 border-t border-gray-100">
          <div>
            <span className="text-sm sm:text-base md:text-lg font-bold text-red-500">${price}</span>
            <span className="text-xs sm:text-sm text-gray-500 ml-1">/Ticket</span>
          </div>
          <button className="text-[#24BDC7] hover:text-[#1a9ba3] font-semibold text-xs sm:text-sm flex items-center transition-colors duration-200">
            See Details
            <svg className="w-2 h-2 sm:w-3 sm:h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TourCard;