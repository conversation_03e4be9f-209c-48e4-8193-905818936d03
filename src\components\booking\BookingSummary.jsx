"use client";
import React from 'react';
import { Star, Edit3, Edit3Icon, Edit, Edit2 } from 'lucide-react';
// import { useTheme } from '@/contexts/ThemeContext';
// import { primaryColors, secondaryColors } from '@/utils/themeUtils';
import ActionButton from '../ui/ActionButton';

const BookingSummary = ({ flightData, onConfirmBooking, loading = false }) => {
  // Handle both old and new flight data structure
  const summaryItems = [
    { label: 'Take Off', value: flightData?.takeOff || flightData?.route?.split(' - ')[0] || '' },
    { label: 'Take Off Time', value: flightData?.takeOffTime || '' },
    { label: 'Landing', value: flightData?.landing || flightData?.route?.split(' - ')[1] || '' },
    { label: 'Landing Time', value: flightData?.landingTime || '' },
    { label: 'Journey Date', value: flightData?.journeyDate || flightData?.journeyDay || '' },
    { label: 'Airline', value: flightData?.airline || '' },
    { label: 'Flight Type', value: flightData?.flightType || '' },
    { label: 'Flight Class', value: flightData?.flightClass || flightData?.passengerClass || '' },
    { label: 'Flight Duration', value: flightData?.flightDuration || '' },
    { label: 'Flight Stop', value: flightData?.stops || flightData?.flightStop || '' },
    { label: 'Passengers', value: flightData?.passengers || flightData?.seatsSequence || flightData?.adults || '' },
    { label: 'Fare Type', value: flightData?.fareType || '' },
    { label: 'Cancellation', value: flightData?.cancellationFee || '' },
    { label: 'Flight Change', value: flightData?.flightChange || '' },
  ];

  const paymentItems = [
    { label: 'Sub Total', value: flightData?.subTotal || flightData?.originalPrice || '' },
    { label: 'Discount', value: flightData?.discount || '' },
    { label: 'Taxes', value: flightData?.taxes || '' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 sticky top-8">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-[#0C2C7A]">Booking Summary</h3>
      </div>
      {/* Flight Image */}
      <div className="mb-6">
        <img 
          src="/assets/img/breadcrumb/01.jpg" 
          alt="Flight destination" 
          className="w-full h-56 object-cover rounded-full rounded-bl-none p-2"
        />
      </div>

      {/* Flight Details */}
      <div className="space-y-4">
        <div>
           <div className="flex items-center justify-between mb-6">
          <h4 className="font-bold text-lg text-[#0C2C7A]">{flightData?.route || ''}</h4>
          <button className="p-2 text-white bg-[#24BDC7] rounded-xl hover:text-[#24BDC7] transition-colors">
          <Edit2 size={20} />
        </button>
        </div>
          <p className="text-sm text-gray-600">{flightData?.type || ''}</p>
          <div className="flex items-center mt-1">
            <div className="flex items-center">
              <Star className="text-yellow-400 fill-current" size={16} />
              <span className="text-sm font-medium ml-1">{flightData?.rating || ''}</span>
            </div>
            <span className="text-sm text-gray-600 ml-2">Average ({flightData?.reviews || ''} Reviews)</span>
          </div>
        </div>

        {/* Order Info */}
        <div className="border-t pt-4">
          <h5 className="font-semibold text-[#0C2C7A] mb-3">Order Info</h5>
          <div className="space-y-2 text-sm">
            {summaryItems.map((item, index) => (
              <div key={index} className="flex justify-between">
                <span className="text-gray-600">{item.label}:</span>
                <span className="font-medium">{item.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Payment Info */}
        <div className="border-t pt-4">
          <h5 className="font-semibold text-[#0C2C7A] mb-3">Booking Payment</h5>
          <div className="space-y-2 text-sm">
            {paymentItems.map((item, index) => (
              <div key={index} className="flex justify-between">
                <span className="text-gray-600">{item.label}:</span>
                <span className="font-medium">{item.value}</span>
              </div>
            ))}
            <div className="flex justify-between border-t pt-2 text-lg font-bold">
              <span className="text-[#0C2C7A]">You Pay:</span>
              <span className="text-[#24BDC7]">{flightData?.youPay || flightData?.price || ''}</span>
            </div>
          </div>
        </div>

        {/* Confirm Booking Button */}
        <div className="mt-6 pt-4 border-t">
           <ActionButton
             text={loading ? "Processing..." : "Confirm Booking"}
             onClick={onConfirmBooking}
             isMobile={true}
             disabled={loading}
           />
        </div>
      </div>
    </div>
  );
};

export default BookingSummary;
