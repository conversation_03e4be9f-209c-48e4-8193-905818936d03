import React from 'react';
import FilterSection from './FilterSection';
import CheckboxItem from './CheckboxItem';

const AirlinesFilter = ({ filters, onFilterChange }) => {
  const airlines = [
    { label: 'American Airlines', count: '25' },
    { label: 'Delta Airlines', count: '15' },
    { label: 'Qatar Airways', count: '18' },
    { label: 'Fly Emirates', count: '25' },
    { label: 'Singapore Airlines', count: '32' }
  ];

  return (
    <FilterSection title="Airlines">
      {airlines.map((airline) => (
        <CheckboxItem
          key={airline.label}
          label={airline.label}
          count={airline.count}
          checked={filters.airlines?.includes(airline.label)}
          onChange={(e) => onFilterChange('airlines', airline.label, e.target.checked)}
        />
      ))}
    </FilterSection>
  );
};

export default AirlinesFilter;
