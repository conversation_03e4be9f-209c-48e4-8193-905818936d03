import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

const DestinationCard = ({ destination }) => {
    const { name, image, rating, reviews } = destination;
    const router = useRouter();

    const handleCardClick = () => {
        // Navigate to popular flights page with destination parameter
        router.push(`/popularFlight?destination=${encodeURIComponent(name.toLowerCase())}`);
    };

    // Generate star rating
    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < 5; i++) {
            if (i < fullStars) {
                stars.push('★');
            } else if (i === fullStars && hasHalfStar) {
                stars.push('☆');
            } else {
                stars.push('☆');
            }
        }
        return stars;
    };

    return (
        <div className="cursor-pointer" onClick={handleCardClick}>
            {/* Oval Image Container with overlaid content */}
            <div className="relative w-48 h-56 sm:w-56 sm:h-64 md:w-64 md:h-72 lg:w-46 lg:h-58 xl:w-56 xl:h-64 mx-auto mb-4">
                <div className="relative w-full h-full rounded-[10rem] overflow-hidden">
                    <Image
                        src={image}
                        alt={name}
                        fill
                        className="object-cover"
                        sizes="(max-width: 640px) 192px, (max-width: 768px) 224px, (max-width: 1024px) 256px, (max-width: 1280px) 192px, 224px"
                    />
                </div>

                {/* Content overlaid on top of the image */}
                <div className="absolute bottom-[-0.8rem] left-5 right-0 text-center bg-white h-[5rem] rounded-[8rem] px-4 py-2 w-[80%] ">
                    {/* Destination Name */}
                    <h3 className="text-lg sm:text-lg font-bold text-[#0C2C7A] mb-2">
                        {name}
                    </h3>

                    {/* Rating and Reviews */}
                    <div className="flex items-center justify-center gap-2">
                        {/* Stars */}
                        <div className="flex text-yellow-400 text-[12px]">
                            {renderStars(rating).map((star, index) => (
                                <span key={index} className={star === '★' ? 'text-yellow-400' : 'text-gray-300'}>
                                    {star === '★' ? '★' : '☆'}
                                </span>
                            ))}
                        </div>
                        
                        {/* Reviews */}
                        <span className="text-[#0C2C7A] text-[12px] truncate">
                            ({reviews} Reviews)
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DestinationCard;
