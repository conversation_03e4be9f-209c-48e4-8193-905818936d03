import axiosInstance from "./axiosInstance";

/**
 * Book a flight with the provided booking data
 * @param {Object} bookingData - The booking information
 * @param {string} bookingData.fareId - The fare ID to book
 * @param {Object} bookingData.passengerDetails - Passenger information
 * @param {Object} bookingData.contactDetails - Contact information
 * @param {Object} bookingData.paymentDetails - Payment information
 * @returns {Promise} - API response
 */
export const bookFlight = (bookingData) => {
  return axiosInstance.post("/api/v1/flight/booking", bookingData);
};

/**
 * Get booking details by booking ID
 * @param {string} bookingId - The booking ID
 * @returns {Promise} - API response
 */
export const getBookingDetails = (bookingId) => {
  return axiosInstance.get(`/api/v1/flight/booking/${bookingId}`);
};

/**
 * Cancel a booking
 * @param {string} bookingId - The booking ID to cancel
 * @returns {Promise} - API response
 */
export const cancelBooking = (bookingId) => {
  return axiosInstance.delete(`/api/v1/flight/booking/${bookingId}`);
};

/**
 * Get user's booking history
 * @param {string} userId - The user ID (optional, can be handled by auth token)
 * @returns {Promise} - API response
 */
export const getBookingHistory = (userId = null) => {
  const endpoint = userId 
    ? `/api/v1/flight/booking/history?userId=${userId}`
    : "/api/v1/flight/booking/history";
  
  return axiosInstance.get(endpoint);
};
