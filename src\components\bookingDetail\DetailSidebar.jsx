"use client";
import React from 'react';
import { BookingWidget, OrganizedBy, ContactCard, WhyBookWithUs } from '.';

const Sidebar = ({ sidebarData = {} }) => {
  const {
    bookingData = {},
    whyBookData = {},
    contactData = {},
    organizerData = {}
  } = sidebarData;

  return (
    <div className="lg:col-span-1">
      <div className="sticky top-6 space-y-6">
        {/* Booking Widget */}
        <BookingWidget {...bookingData} />

        {/* Why Book With Us */}
        <WhyBookWithUs title={whyBookData.title} features={whyBookData.features} />

        {/* Get A Question */}
        <ContactCard {...contactData} />

        {/* Organized By */}
        <OrganizedBy {...organizerData} />
      </div>
    </div>

  );
};

export default Sidebar;