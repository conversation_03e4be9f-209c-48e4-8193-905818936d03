"use client";
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createFlightBooking } from '@/redux/slices/flightBookingSlice';
import PersonalInfoForm from './PersonalInfoForm';
import PaymentForm from './PaymentForm';
import BookingSummary from './BookingSummary';

const FlightBookingForm = () => {
  const dispatch = useDispatch();
  const { booking, loading, error } = useSelector((state) => state.flightBooking);

  const [fareId, setFareId] = useState(null);
  const [selectedFlightData, setSelectedFlightData] = useState(null);
  const [bookingData, setBookingData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address1: '',
    address2: '',
    country: '',
    age: '',
    city: '',
    state: '',
    zipCode: '',
    additionalInfo: '',
    
    // Payment Information
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardHolderName: '',

    // PayPal Information
    paypalEmail: '',
    paypalPassword: '',

    // Payoneer Information
    payoneerEmail: '',
    payoneerPassword: ''
  });

  // Mock flight data
  const flightData = {
    route: 'New York – Los Angeles',
    type: 'One Way Flight',
    rating: 4.5,
    reviews: 35,
    takeOff: 'New York',
    landing: 'Los Angeles',
    journeyDate: '20 Aug 2025 at 10:10 AM',
    airline: 'Delta',
    flightType: 'One Way',
    flightClass: 'Economy',
    flightDuration: '4h 05m',
    flightStop: 'Non Stop',
    adults: 4,
    children: 2,
    subTotal: '$50,540.00',
    discount: '$600.00',
    taxes: '$560.00',
    youPay: '$51,543.00'
  };

  // Load fareId and flight data from localStorage on component mount
  useEffect(() => {
    const storedFareId = localStorage.getItem('selectedFareId');
    const storedFlightData = localStorage.getItem('selectedFlightData');

    if (storedFareId) {
      setFareId(storedFareId);
    }

    if (storedFlightData) {
      try {
        const parsedFlightData = JSON.parse(storedFlightData);
        setSelectedFlightData(parsedFlightData);
      } catch (error) {
        console.error('Error parsing flight data:', error);
      }
    }
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBookingData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!fareId) {
      alert('No flight selected. Please go back to flight details page.');
      return;
    }

    // Validate required fields
    if (!bookingData.firstName || !bookingData.lastName || !bookingData.email || !bookingData.phone) {
      alert('Please fill in all required personal information fields.');
      return;
    }

    // Prepare booking data for API - Match Postman structure
    const bookingPayload = {
      fareId: fareId,
      personalInfo: {
        firstName: bookingData.firstName || '',
        lastName: bookingData.lastName || '',
        email: bookingData.email || '',
        phone: bookingData.phone || '',
        addressLine1: bookingData.address1 || '',
        addressLine2: bookingData.address2 || '',
        age: parseInt(bookingData.age) || 25,
        city: bookingData.city || '',
        state: bookingData.state || '',
        zipCode: bookingData.zipCode || '',
        additionalComment: bookingData.additionalInfo || ''
      }
    };

    // Dispatch booking action
    dispatch(createFlightBooking(bookingPayload));
  };

  // Show success message if booking is successful
  if (booking) {
    return (
      <div className="container mx-auto px-4 py-8 mb-20">
        <div className="max-w-2xl mx-auto text-center">
          <div className="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg mb-6">
            <h2 className="text-2xl font-bold mb-2">🎉 Booking Successful!</h2>
            <p className="text-lg">Your flight has been booked successfully.</p>
            {booking.bookingId && (
              <p className="text-sm mt-2">Booking ID: <strong>{booking.bookingId}</strong></p>
            )}
          </div>
          <button
            onClick={() => window.location.href = '/en/flight-details'}
            className="bg-[#24BDC7] hover:bg-[#1ea5ae] text-white font-bold py-3 px-6 rounded-lg transition-colors"
          >
            Book Another Flight
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-20">
      {/* Show error message if booking failed */}
      {error && (
        <div className="max-w-2xl mx-auto mb-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong className="font-bold">Error!</strong>
            <span className="block sm:inline"> {error}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Booking Form */}
        <div className="lg:col-span-2 space-y-6">
          <PersonalInfoForm
            formData={bookingData}
            onChange={handleInputChange}
          />

          <PaymentForm
            formData={bookingData}
            onChange={handleInputChange}
            onSubmit={handleSubmit}
            loading={loading}
          />
        </div>

        {/* Right Column - Booking Summary */}
        <div className="lg:col-span-1">
          <BookingSummary
            flightData={selectedFlightData || flightData}
            onConfirmBooking={handleSubmit}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default FlightBookingForm;
