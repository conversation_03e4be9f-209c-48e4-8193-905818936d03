"use client";
import React, { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle, HelpingHand } from 'lucide-react';
import { FaQuestion } from 'react-icons/fa';

const FAQ = ({ faqs = [], title = "Faq's" }) => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="p-6">
      <h3 className="text-xl font-bold text-[#0C2C7A] mb-6">{title}</h3>
      
      <div className="space-y-4">
        {faqs.map((faq, index) => (
          <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
            <button
              className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
              onClick={() => toggleFAQ(index)}
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-[#24BDC7] rounded-full flex items-center justify-center flex-shrink-0">
                  <HelpCircle size={20} className="text-white" />
                </div>
                <span className="font-medium text-[#0C2C7A] text-sm md:text-base">
                  {faq.question}
                </span>
              </div>
              <div className="flex-shrink-0 ml-2">
                {openIndex === index ? (
                  <ChevronUp size={20} className="text-gray-400" />
                ) : (
                  <ChevronDown size={20} className="text-gray-400" />
                )}
              </div>
            </button>
            
            {openIndex === index && (
              <div className="px-4 pb-4 pt-0">
                <div className="pl-11">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default FAQ;
