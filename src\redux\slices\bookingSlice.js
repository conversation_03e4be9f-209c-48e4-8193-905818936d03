// src/redux/slices/bookingSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  activeTab: 'flights',
  tripType: 'oneWay',
  hotelTripType: 'single',
  // Single trip states
  fromLocation: {
    city: '',
    airport: '',
    cityCode: '',
    countryCode: '',
    countryName: ''
  },
  toLocation: {
    city: '',
    airport: '',
    cityCode: '',
    countryCode: '',
    countryName: ''
  },
  departureDate: null,
  returnDate: null,
  passengers: {
    adults: 1,
    children: 0,
    infants: 0
  },
  travelClass: 'economy',
  // Multi-city flight segments
  flightSegments: [],
  // Hotel booking states
  hotelDestination: {
    city: '',
    country: '',
    attractions: ''
  },
  checkInDate: null,
  checkOutDate: null,
  hotelRooms: [{
    adults: 2,
    children: 0,
    roomType: 'Double Room'
  }],
  // Multi-city hotel segments
  hotelSegments: []
};

const bookingSlice = createSlice({
  name: 'booking',
  initialState,
  reducers: {
    setBookingData: (state, action) => {
      return { ...state, ...action.payload };
    },
    updateBookingField: (state, action) => {
      const { field, value } = action.payload;
      state[field] = value;
    },
    resetBookingData: (state) => {
      return initialState;
    }
  },
});

export const { setBookingData, updateBookingField, resetBookingData } = bookingSlice.actions;
export default bookingSlice.reducer;