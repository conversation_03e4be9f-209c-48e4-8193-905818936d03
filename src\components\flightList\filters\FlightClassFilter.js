import React from 'react';
import FilterSection from './FilterSection';
import CheckboxItem from './CheckboxItem';

const FlightClassFilter = ({ filters, onFilterChange }) => {
  const flightClasses = [
    { label: 'Business', count: '20' },
    { label: 'First Class', count: '15' },
    { label: 'Economy', count: '18' }
  ];

  return (
    <FilterSection title="Flight Class">
      {flightClasses.map((flightClass) => (
        <CheckboxItem
          key={flightClass.label}
          label={flightClass.label}
          count={flightClass.count}
          checked={filters.flightClass?.includes(flightClass.label)}
          onChange={(e) => onFilterChange('flightClass', flightClass.label, e.target.checked)}
        />
      ))}
    </FilterSection>
  );
};

export default FlightClassFilter;
